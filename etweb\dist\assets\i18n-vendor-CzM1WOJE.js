import{r as I}from"./router-vendor-CX6yTN5-.js";const Pe=(r,e,t,s)=>{var n,a,o,u;const i=[t,{code:e,...s||{}}];if((a=(n=r==null?void 0:r.services)==null?void 0:n.logger)!=null&&a.forward)return r.services.logger.forward(i,"warn","react-i18next::",!0);A(i[0])&&(i[0]=`react-i18next:: ${i[0]}`),(u=(o=r==null?void 0:r.services)==null?void 0:o.logger)!=null&&u.warn?r.services.logger.warn(...i):console!=null&&console.warn&&console.warn(...i)},oe={},q=(r,e,t,s)=>{A(t)&&oe[t]||(A(t)&&(oe[t]=new Date),Pe(r,e,t,s))},we=(r,e)=>()=>{if(r.isInitialized)e();else{const t=()=>{setTimeout(()=>{r.off("initialized",t)},0),e()};r.on("initialized",t)}},ee=(r,e,t)=>{r.loadNamespaces(e,we(r,t))},le=(r,e,t,s)=>{if(A(t)&&(t=[t]),r.options.preload&&r.options.preload.indexOf(e)>-1)return ee(r,t,s);t.forEach(i=>{r.options.ns.indexOf(i)<0&&r.options.ns.push(i)}),r.loadLanguages(e,we(r,s))},ke=(r,e,t={})=>!e.languages||!e.languages.length?(q(e,"NO_LANGUAGES","i18n.languages were undefined or empty",{languages:e.languages}),!0):e.hasLoadedNamespace(r,{lng:t.lng,precheck:(s,i)=>{var n;if(((n=t.bindI18n)==null?void 0:n.indexOf("languageChanging"))>-1&&s.services.backendConnector.backend&&s.isLanguageChangingTo&&!i(s.isLanguageChangingTo,r))return!1}}),A=r=>typeof r=="string",Ee=r=>typeof r=="object"&&r!==null,Fe=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,je={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"©","&#169;":"©","&reg;":"®","&#174;":"®","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},Te=r=>je[r],Ie=r=>r.replace(Fe,Te);let te={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:Ie};const De=(r={})=>{te={...te,...r}},Ve=()=>te;let Ce;const Ae=r=>{Ce=r},Ke=()=>Ce,ft={type:"3rdParty",init(r){De(r.options.react),Ae(r)}},Ue=I.createContext();class Me{constructor(){this.usedNamespaces={}}addUsedNamespaces(e){e.forEach(t=>{this.usedNamespaces[t]||(this.usedNamespaces[t]=!0)})}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}const He=(r,e)=>{const t=I.useRef();return I.useEffect(()=>{t.current=r},[r,e]),t.current},Ne=(r,e,t,s)=>r.getFixedT(e,t,s),ze=(r,e,t,s)=>I.useCallback(Ne(r,e,t,s),[r,e,t,s]),ct=(r,e={})=>{var L,v,D,$;const{i18n:t}=e,{i18n:s,defaultNS:i}=I.useContext(Ue)||{},n=t||s||Ke();if(n&&!n.reportNamespaces&&(n.reportNamespaces=new Me),!n){q(n,"NO_I18NEXT_INSTANCE","useTranslation: You will need to pass in an i18next instance by using initReactI18next");const O=(x,P)=>A(P)?P:Ee(P)&&A(P.defaultValue)?P.defaultValue:Array.isArray(x)?x[x.length-1]:x,y=[O,{},!1];return y.t=O,y.i18n={},y.ready=!1,y}(L=n.options.react)!=null&&L.wait&&q(n,"DEPRECATED_OPTION","useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");const a={...Ve(),...n.options.react,...e},{useSuspense:o,keyPrefix:u}=a;let l=i||((v=n.options)==null?void 0:v.defaultNS);l=A(l)?[l]:l||["translation"],($=(D=n.reportNamespaces).addUsedNamespaces)==null||$.call(D,l);const f=(n.isInitialized||n.initializedStoreOnce)&&l.every(O=>ke(O,n,a)),c=ze(n,e.lng||null,a.nsMode==="fallback"?l:l[0],u),g=()=>c,d=()=>Ne(n,e.lng||null,a.nsMode==="fallback"?l:l[0],u),[h,m]=I.useState(g);let S=l.join();e.lng&&(S=`${e.lng}${S}`);const C=He(S),b=I.useRef(!0);I.useEffect(()=>{const{bindI18n:O,bindI18nStore:y}=a;b.current=!0,!f&&!o&&(e.lng?le(n,e.lng,l,()=>{b.current&&m(d)}):ee(n,l,()=>{b.current&&m(d)})),f&&C&&C!==S&&b.current&&m(d);const x=()=>{b.current&&m(d)};return O&&(n==null||n.on(O,x)),y&&(n==null||n.store.on(y,x)),()=>{b.current=!1,n&&(O==null||O.split(" ").forEach(P=>n.off(P,x))),y&&n&&y.split(" ").forEach(P=>n.store.off(P,x))}},[n,S]),I.useEffect(()=>{b.current&&f&&m(g)},[n,u,f]);const N=[h,n,f];if(N.t=h,N.i18n=n,N.ready=f,f||!f&&!o)return N;throw new Promise(O=>{e.lng?le(n,e.lng,l,()=>O()):ee(n,l,()=>O())})},p=r=>typeof r=="string",H=()=>{let r,e;const t=new Promise((s,i)=>{r=s,e=i});return t.resolve=r,t.reject=e,t},ue=r=>r==null?"":""+r,Be=(r,e,t)=>{r.forEach(s=>{e[s]&&(t[s]=e[s])})},Je=/###/g,fe=r=>r&&r.indexOf("###")>-1?r.replace(Je,"."):r,ce=r=>!r||p(r),z=(r,e,t)=>{const s=p(e)?e.split("."):e;let i=0;for(;i<s.length-1;){if(ce(r))return{};const n=fe(s[i]);!r[n]&&t&&(r[n]=new t),Object.prototype.hasOwnProperty.call(r,n)?r=r[n]:r={},++i}return ce(r)?{}:{obj:r,k:fe(s[i])}},he=(r,e,t)=>{const{obj:s,k:i}=z(r,e,Object);if(s!==void 0||e.length===1){s[i]=t;return}let n=e[e.length-1],a=e.slice(0,e.length-1),o=z(r,a,Object);for(;o.obj===void 0&&a.length;)n=`${a[a.length-1]}.${n}`,a=a.slice(0,a.length-1),o=z(r,a,Object),o!=null&&o.obj&&typeof o.obj[`${o.k}.${n}`]<"u"&&(o.obj=void 0);o.obj[`${o.k}.${n}`]=t},We=(r,e,t,s)=>{const{obj:i,k:n}=z(r,e,Object);i[n]=i[n]||[],i[n].push(t)},G=(r,e)=>{const{obj:t,k:s}=z(r,e);if(t&&Object.prototype.hasOwnProperty.call(t,s))return t[s]},_e=(r,e,t)=>{const s=G(r,t);return s!==void 0?s:G(e,t)},Re=(r,e,t)=>{for(const s in e)s!=="__proto__"&&s!=="constructor"&&(s in r?p(r[s])||r[s]instanceof String||p(e[s])||e[s]instanceof String?t&&(r[s]=e[s]):Re(r[s],e[s],t):r[s]=e[s]);return r},K=r=>r.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var Ge={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};const Ye=r=>p(r)?r.replace(/[&<>"'\/]/g,e=>Ge[e]):r;class Qe{constructor(e){this.capacity=e,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(e){const t=this.regExpMap.get(e);if(t!==void 0)return t;const s=new RegExp(e);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(e,s),this.regExpQueue.push(e),s}}const Ze=[" ",",","?","!",";"],Xe=new Qe(20),qe=(r,e,t)=>{e=e||"",t=t||"";const s=Ze.filter(a=>e.indexOf(a)<0&&t.indexOf(a)<0);if(s.length===0)return!0;const i=Xe.getRegExp(`(${s.map(a=>a==="?"?"\\?":a).join("|")})`);let n=!i.test(r);if(!n){const a=r.indexOf(t);a>0&&!i.test(r.substring(0,a))&&(n=!0)}return n},se=(r,e,t=".")=>{if(!r)return;if(r[e])return Object.prototype.hasOwnProperty.call(r,e)?r[e]:void 0;const s=e.split(t);let i=r;for(let n=0;n<s.length;){if(!i||typeof i!="object")return;let a,o="";for(let u=n;u<s.length;++u)if(u!==n&&(o+=t),o+=s[u],a=i[o],a!==void 0){if(["string","number","boolean"].indexOf(typeof a)>-1&&u<s.length-1)continue;n+=u-n+1;break}i=a}return i},B=r=>r==null?void 0:r.replace("_","-"),et={type:"logger",log(r){this.output("log",r)},warn(r){this.output("warn",r)},error(r){this.output("error",r)},output(r,e){var t,s;(s=(t=console==null?void 0:console[r])==null?void 0:t.apply)==null||s.call(t,console,e)}};class Y{constructor(e,t={}){this.init(e,t)}init(e,t={}){this.prefix=t.prefix||"i18next:",this.logger=e||et,this.options=t,this.debug=t.debug}log(...e){return this.forward(e,"log","",!0)}warn(...e){return this.forward(e,"warn","",!0)}error(...e){return this.forward(e,"error","")}deprecate(...e){return this.forward(e,"warn","WARNING DEPRECATED: ",!0)}forward(e,t,s,i){return i&&!this.debug?null:(p(e[0])&&(e[0]=`${s}${this.prefix} ${e[0]}`),this.logger[t](e))}create(e){return new Y(this.logger,{prefix:`${this.prefix}:${e}:`,...this.options})}clone(e){return e=e||this.options,e.prefix=e.prefix||this.prefix,new Y(this.logger,e)}}var j=new Y;class Z{constructor(){this.observers={}}on(e,t){return e.split(" ").forEach(s=>{this.observers[s]||(this.observers[s]=new Map);const i=this.observers[s].get(t)||0;this.observers[s].set(t,i+1)}),this}off(e,t){if(this.observers[e]){if(!t){delete this.observers[e];return}this.observers[e].delete(t)}}emit(e,...t){this.observers[e]&&Array.from(this.observers[e].entries()).forEach(([i,n])=>{for(let a=0;a<n;a++)i(...t)}),this.observers["*"]&&Array.from(this.observers["*"].entries()).forEach(([i,n])=>{for(let a=0;a<n;a++)i.apply(i,[e,...t])})}}class de extends Z{constructor(e,t={ns:["translation"],defaultNS:"translation"}){super(),this.data=e||{},this.options=t,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.options.ignoreJSONStructure===void 0&&(this.options.ignoreJSONStructure=!0)}addNamespaces(e){this.options.ns.indexOf(e)<0&&this.options.ns.push(e)}removeNamespaces(e){const t=this.options.ns.indexOf(e);t>-1&&this.options.ns.splice(t,1)}getResource(e,t,s,i={}){var l,f;const n=i.keySeparator!==void 0?i.keySeparator:this.options.keySeparator,a=i.ignoreJSONStructure!==void 0?i.ignoreJSONStructure:this.options.ignoreJSONStructure;let o;e.indexOf(".")>-1?o=e.split("."):(o=[e,t],s&&(Array.isArray(s)?o.push(...s):p(s)&&n?o.push(...s.split(n)):o.push(s)));const u=G(this.data,o);return!u&&!t&&!s&&e.indexOf(".")>-1&&(e=o[0],t=o[1],s=o.slice(2).join(".")),u||!a||!p(s)?u:se((f=(l=this.data)==null?void 0:l[e])==null?void 0:f[t],s,n)}addResource(e,t,s,i,n={silent:!1}){const a=n.keySeparator!==void 0?n.keySeparator:this.options.keySeparator;let o=[e,t];s&&(o=o.concat(a?s.split(a):s)),e.indexOf(".")>-1&&(o=e.split("."),i=t,t=o[1]),this.addNamespaces(t),he(this.data,o,i),n.silent||this.emit("added",e,t,s,i)}addResources(e,t,s,i={silent:!1}){for(const n in s)(p(s[n])||Array.isArray(s[n]))&&this.addResource(e,t,n,s[n],{silent:!0});i.silent||this.emit("added",e,t,s)}addResourceBundle(e,t,s,i,n,a={silent:!1,skipCopy:!1}){let o=[e,t];e.indexOf(".")>-1&&(o=e.split("."),i=s,s=t,t=o[1]),this.addNamespaces(t);let u=G(this.data,o)||{};a.skipCopy||(s=JSON.parse(JSON.stringify(s))),i?Re(u,s,n):u={...u,...s},he(this.data,o,u),a.silent||this.emit("added",e,t,s)}removeResourceBundle(e,t){this.hasResourceBundle(e,t)&&delete this.data[e][t],this.removeNamespaces(t),this.emit("removed",e,t)}hasResourceBundle(e,t){return this.getResource(e,t)!==void 0}getResourceBundle(e,t){return t||(t=this.options.defaultNS),this.getResource(e,t)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){const t=this.getDataByLanguage(e);return!!(t&&Object.keys(t)||[]).find(i=>t[i]&&Object.keys(t[i]).length>0)}toJSON(){return this.data}}var ve={processors:{},addPostProcessor(r){this.processors[r.name]=r},handle(r,e,t,s,i){return r.forEach(n=>{var a;e=((a=this.processors[n])==null?void 0:a.process(e,t,s,i))??e}),e}};const ge={},pe=r=>!p(r)&&typeof r!="boolean"&&typeof r!="number";class Q extends Z{constructor(e,t={}){super(),Be(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],e,this),this.options=t,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.logger=j.create("translator")}changeLanguage(e){e&&(this.language=e)}exists(e,t={interpolation:{}}){const s={...t};if(e==null)return!1;const i=this.resolve(e,s);return(i==null?void 0:i.res)!==void 0}extractFromKey(e,t){let s=t.nsSeparator!==void 0?t.nsSeparator:this.options.nsSeparator;s===void 0&&(s=":");const i=t.keySeparator!==void 0?t.keySeparator:this.options.keySeparator;let n=t.ns||this.options.defaultNS||[];const a=s&&e.indexOf(s)>-1,o=!this.options.userDefinedKeySeparator&&!t.keySeparator&&!this.options.userDefinedNsSeparator&&!t.nsSeparator&&!qe(e,s,i);if(a&&!o){const u=e.match(this.interpolator.nestingRegexp);if(u&&u.length>0)return{key:e,namespaces:p(n)?[n]:n};const l=e.split(s);(s!==i||s===i&&this.options.ns.indexOf(l[0])>-1)&&(n=l.shift()),e=l.join(i)}return{key:e,namespaces:p(n)?[n]:n}}translate(e,t,s){let i=typeof t=="object"?{...t}:t;if(typeof i!="object"&&this.options.overloadTranslationOptionHandler&&(i=this.options.overloadTranslationOptionHandler(arguments)),typeof options=="object"&&(i={...i}),i||(i={}),e==null)return"";Array.isArray(e)||(e=[String(e)]);const n=i.returnDetails!==void 0?i.returnDetails:this.options.returnDetails,a=i.keySeparator!==void 0?i.keySeparator:this.options.keySeparator,{key:o,namespaces:u}=this.extractFromKey(e[e.length-1],i),l=u[u.length-1];let f=i.nsSeparator!==void 0?i.nsSeparator:this.options.nsSeparator;f===void 0&&(f=":");const c=i.lng||this.language,g=i.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if((c==null?void 0:c.toLowerCase())==="cimode")return g?n?{res:`${l}${f}${o}`,usedKey:o,exactUsedKey:o,usedLng:c,usedNS:l,usedParams:this.getUsedParamsDetails(i)}:`${l}${f}${o}`:n?{res:o,usedKey:o,exactUsedKey:o,usedLng:c,usedNS:l,usedParams:this.getUsedParamsDetails(i)}:o;const d=this.resolve(e,i);let h=d==null?void 0:d.res;const m=(d==null?void 0:d.usedKey)||o,S=(d==null?void 0:d.exactUsedKey)||o,C=["[object Number]","[object Function]","[object RegExp]"],b=i.joinArrays!==void 0?i.joinArrays:this.options.joinArrays,N=!this.i18nFormat||this.i18nFormat.handleAsObject,L=i.count!==void 0&&!p(i.count),v=Q.hasDefaultValue(i),D=L?this.pluralResolver.getSuffix(c,i.count,i):"",$=i.ordinal&&L?this.pluralResolver.getSuffix(c,i.count,{ordinal:!1}):"",O=L&&!i.ordinal&&i.count===0,y=O&&i[`defaultValue${this.options.pluralSeparator}zero`]||i[`defaultValue${D}`]||i[`defaultValue${$}`]||i.defaultValue;let x=h;N&&!h&&v&&(x=y);const P=pe(x),$e=Object.prototype.toString.apply(x);if(N&&x&&P&&C.indexOf($e)<0&&!(p(b)&&Array.isArray(x))){if(!i.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");const E=this.options.returnedObjectHandler?this.options.returnedObjectHandler(m,x,{...i,ns:u}):`key '${o} (${this.language})' returned an object instead of string.`;return n?(d.res=E,d.usedParams=this.getUsedParamsDetails(i),d):E}if(a){const E=Array.isArray(x),k=E?[]:{},ie=E?S:m;for(const F in x)if(Object.prototype.hasOwnProperty.call(x,F)){const T=`${ie}${a}${F}`;v&&!h?k[F]=this.translate(T,{...i,defaultValue:pe(y)?y[F]:void 0,joinArrays:!1,ns:u}):k[F]=this.translate(T,{...i,joinArrays:!1,ns:u}),k[F]===T&&(k[F]=x[F])}h=k}}else if(N&&p(b)&&Array.isArray(h))h=h.join(b),h&&(h=this.extendTranslation(h,e,i,s));else{let E=!1,k=!1;!this.isValidLookup(h)&&v&&(E=!0,h=y),this.isValidLookup(h)||(k=!0,h=o);const F=(i.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&k?void 0:h,T=v&&y!==h&&this.options.updateMissing;if(k||E||T){if(this.logger.log(T?"updateKey":"missingKey",c,l,o,T?y:h),a){const R=this.resolve(o,{...i,keySeparator:!1});R&&R.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let U=[];const W=this.languageUtils.getFallbackCodes(this.options.fallbackLng,i.lng||this.language);if(this.options.saveMissingTo==="fallback"&&W&&W[0])for(let R=0;R<W.length;R++)U.push(W[R]);else this.options.saveMissingTo==="all"?U=this.languageUtils.toResolveHierarchy(i.lng||this.language):U.push(i.lng||this.language);const ne=(R,V,M)=>{var ae;const re=v&&M!==h?M:F;this.options.missingKeyHandler?this.options.missingKeyHandler(R,l,V,re,T,i):(ae=this.backendConnector)!=null&&ae.saveMissing&&this.backendConnector.saveMissing(R,l,V,re,T,i),this.emit("missingKey",R,l,V,h)};this.options.saveMissing&&(this.options.saveMissingPlurals&&L?U.forEach(R=>{const V=this.pluralResolver.getSuffixes(R,i);O&&i[`defaultValue${this.options.pluralSeparator}zero`]&&V.indexOf(`${this.options.pluralSeparator}zero`)<0&&V.push(`${this.options.pluralSeparator}zero`),V.forEach(M=>{ne([R],o+M,i[`defaultValue${M}`]||y)})}):ne(U,o,y))}h=this.extendTranslation(h,e,i,d,s),k&&h===o&&this.options.appendNamespaceToMissingKey&&(h=`${l}${f}${o}`),(k||E)&&this.options.parseMissingKeyHandler&&(h=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${l}${f}${o}`:o,E?h:void 0,i))}return n?(d.res=h,d.usedParams=this.getUsedParamsDetails(i),d):h}extendTranslation(e,t,s,i,n){var u,l;if((u=this.i18nFormat)!=null&&u.parse)e=this.i18nFormat.parse(e,{...this.options.interpolation.defaultVariables,...s},s.lng||this.language||i.usedLng,i.usedNS,i.usedKey,{resolved:i});else if(!s.skipInterpolation){s.interpolation&&this.interpolator.init({...s,interpolation:{...this.options.interpolation,...s.interpolation}});const f=p(e)&&(((l=s==null?void 0:s.interpolation)==null?void 0:l.skipOnVariables)!==void 0?s.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);let c;if(f){const d=e.match(this.interpolator.nestingRegexp);c=d&&d.length}let g=s.replace&&!p(s.replace)?s.replace:s;if(this.options.interpolation.defaultVariables&&(g={...this.options.interpolation.defaultVariables,...g}),e=this.interpolator.interpolate(e,g,s.lng||this.language||i.usedLng,s),f){const d=e.match(this.interpolator.nestingRegexp),h=d&&d.length;c<h&&(s.nest=!1)}!s.lng&&i&&i.res&&(s.lng=this.language||i.usedLng),s.nest!==!1&&(e=this.interpolator.nest(e,(...d)=>(n==null?void 0:n[0])===d[0]&&!s.context?(this.logger.warn(`It seems you are nesting recursively key: ${d[0]} in key: ${t[0]}`),null):this.translate(...d,t),s)),s.interpolation&&this.interpolator.reset()}const a=s.postProcess||this.options.postProcess,o=p(a)?[a]:a;return e!=null&&(o!=null&&o.length)&&s.applyPostProcessor!==!1&&(e=ve.handle(o,e,t,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...i,usedParams:this.getUsedParamsDetails(s)},...s}:s,this)),e}resolve(e,t={}){let s,i,n,a,o;return p(e)&&(e=[e]),e.forEach(u=>{if(this.isValidLookup(s))return;const l=this.extractFromKey(u,t),f=l.key;i=f;let c=l.namespaces;this.options.fallbackNS&&(c=c.concat(this.options.fallbackNS));const g=t.count!==void 0&&!p(t.count),d=g&&!t.ordinal&&t.count===0,h=t.context!==void 0&&(p(t.context)||typeof t.context=="number")&&t.context!=="",m=t.lngs?t.lngs:this.languageUtils.toResolveHierarchy(t.lng||this.language,t.fallbackLng);c.forEach(S=>{var C,b;this.isValidLookup(s)||(o=S,!ge[`${m[0]}-${S}`]&&((C=this.utils)!=null&&C.hasLoadedNamespace)&&!((b=this.utils)!=null&&b.hasLoadedNamespace(o))&&(ge[`${m[0]}-${S}`]=!0,this.logger.warn(`key "${i}" for languages "${m.join(", ")}" won't get resolved as namespace "${o}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),m.forEach(N=>{var D;if(this.isValidLookup(s))return;a=N;const L=[f];if((D=this.i18nFormat)!=null&&D.addLookupKeys)this.i18nFormat.addLookupKeys(L,f,N,S,t);else{let $;g&&($=this.pluralResolver.getSuffix(N,t.count,t));const O=`${this.options.pluralSeparator}zero`,y=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(g&&(L.push(f+$),t.ordinal&&$.indexOf(y)===0&&L.push(f+$.replace(y,this.options.pluralSeparator)),d&&L.push(f+O)),h){const x=`${f}${this.options.contextSeparator}${t.context}`;L.push(x),g&&(L.push(x+$),t.ordinal&&$.indexOf(y)===0&&L.push(x+$.replace(y,this.options.pluralSeparator)),d&&L.push(x+O))}}let v;for(;v=L.pop();)this.isValidLookup(s)||(n=v,s=this.getResource(N,S,v,t))}))})}),{res:s,usedKey:i,exactUsedKey:n,usedLng:a,usedNS:o}}isValidLookup(e){return e!==void 0&&!(!this.options.returnNull&&e===null)&&!(!this.options.returnEmptyString&&e==="")}getResource(e,t,s,i={}){var n;return(n=this.i18nFormat)!=null&&n.getResource?this.i18nFormat.getResource(e,t,s,i):this.resourceStore.getResource(e,t,s,i)}getUsedParamsDetails(e={}){const t=["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"],s=e.replace&&!p(e.replace);let i=s?e.replace:e;if(s&&typeof e.count<"u"&&(i.count=e.count),this.options.interpolation.defaultVariables&&(i={...this.options.interpolation.defaultVariables,...i}),!s){i={...i};for(const n of t)delete i[n]}return i}static hasDefaultValue(e){const t="defaultValue";for(const s in e)if(Object.prototype.hasOwnProperty.call(e,s)&&t===s.substring(0,t.length)&&e[s]!==void 0)return!0;return!1}}class me{constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=j.create("languageUtils")}getScriptPartFromCode(e){if(e=B(e),!e||e.indexOf("-")<0)return null;const t=e.split("-");return t.length===2||(t.pop(),t[t.length-1].toLowerCase()==="x")?null:this.formatLanguageCode(t.join("-"))}getLanguagePartFromCode(e){if(e=B(e),!e||e.indexOf("-")<0)return e;const t=e.split("-");return this.formatLanguageCode(t[0])}formatLanguageCode(e){if(p(e)&&e.indexOf("-")>-1){let t;try{t=Intl.getCanonicalLocales(e)[0]}catch{}return t&&this.options.lowerCaseLng&&(t=t.toLowerCase()),t||(this.options.lowerCaseLng?e.toLowerCase():e)}return this.options.cleanCode||this.options.lowerCaseLng?e.toLowerCase():e}isSupportedCode(e){return(this.options.load==="languageOnly"||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}getBestMatchFromCodes(e){if(!e)return null;let t;return e.forEach(s=>{if(t)return;const i=this.formatLanguageCode(s);(!this.options.supportedLngs||this.isSupportedCode(i))&&(t=i)}),!t&&this.options.supportedLngs&&e.forEach(s=>{if(t)return;const i=this.getScriptPartFromCode(s);if(this.isSupportedCode(i))return t=i;const n=this.getLanguagePartFromCode(s);if(this.isSupportedCode(n))return t=n;t=this.options.supportedLngs.find(a=>{if(a===n)return a;if(!(a.indexOf("-")<0&&n.indexOf("-")<0)&&(a.indexOf("-")>0&&n.indexOf("-")<0&&a.substring(0,a.indexOf("-"))===n||a.indexOf(n)===0&&n.length>1))return a})}),t||(t=this.getFallbackCodes(this.options.fallbackLng)[0]),t}getFallbackCodes(e,t){if(!e)return[];if(typeof e=="function"&&(e=e(t)),p(e)&&(e=[e]),Array.isArray(e))return e;if(!t)return e.default||[];let s=e[t];return s||(s=e[this.getScriptPartFromCode(t)]),s||(s=e[this.formatLanguageCode(t)]),s||(s=e[this.getLanguagePartFromCode(t)]),s||(s=e.default),s||[]}toResolveHierarchy(e,t){const s=this.getFallbackCodes((t===!1?[]:t)||this.options.fallbackLng||[],e),i=[],n=a=>{a&&(this.isSupportedCode(a)?i.push(a):this.logger.warn(`rejecting language code not found in supportedLngs: ${a}`))};return p(e)&&(e.indexOf("-")>-1||e.indexOf("_")>-1)?(this.options.load!=="languageOnly"&&n(this.formatLanguageCode(e)),this.options.load!=="languageOnly"&&this.options.load!=="currentOnly"&&n(this.getScriptPartFromCode(e)),this.options.load!=="currentOnly"&&n(this.getLanguagePartFromCode(e))):p(e)&&n(this.formatLanguageCode(e)),s.forEach(a=>{i.indexOf(a)<0&&n(this.formatLanguageCode(a))}),i}}const xe={zero:0,one:1,two:2,few:3,many:4,other:5},ye={select:r=>r===1?"one":"other",resolvedOptions:()=>({pluralCategories:["one","other"]})};class tt{constructor(e,t={}){this.languageUtils=e,this.options=t,this.logger=j.create("pluralResolver"),this.pluralRulesCache={}}addRule(e,t){this.rules[e]=t}clearCache(){this.pluralRulesCache={}}getRule(e,t={}){const s=B(e==="dev"?"en":e),i=t.ordinal?"ordinal":"cardinal",n=JSON.stringify({cleanedCode:s,type:i});if(n in this.pluralRulesCache)return this.pluralRulesCache[n];let a;try{a=new Intl.PluralRules(s,{type:i})}catch{if(!Intl)return this.logger.error("No Intl support, please use an Intl polyfill!"),ye;if(!e.match(/-|_/))return ye;const u=this.languageUtils.getLanguagePartFromCode(e);a=this.getRule(u,t)}return this.pluralRulesCache[n]=a,a}needsPlural(e,t={}){let s=this.getRule(e,t);return s||(s=this.getRule("dev",t)),(s==null?void 0:s.resolvedOptions().pluralCategories.length)>1}getPluralFormsOfKey(e,t,s={}){return this.getSuffixes(e,s).map(i=>`${t}${i}`)}getSuffixes(e,t={}){let s=this.getRule(e,t);return s||(s=this.getRule("dev",t)),s?s.resolvedOptions().pluralCategories.sort((i,n)=>xe[i]-xe[n]).map(i=>`${this.options.prepend}${t.ordinal?`ordinal${this.options.prepend}`:""}${i}`):[]}getSuffix(e,t,s={}){const i=this.getRule(e,s);return i?`${this.options.prepend}${s.ordinal?`ordinal${this.options.prepend}`:""}${i.select(t)}`:(this.logger.warn(`no plural rule found for: ${e}`),this.getSuffix("dev",t,s))}}const Se=(r,e,t,s=".",i=!0)=>{let n=_e(r,e,t);return!n&&i&&p(t)&&(n=se(r,t,s),n===void 0&&(n=se(e,t,s))),n},X=r=>r.replace(/\$/g,"$$$$");class st{constructor(e={}){var t;this.logger=j.create("interpolator"),this.options=e,this.format=((t=e==null?void 0:e.interpolation)==null?void 0:t.format)||(s=>s),this.init(e)}init(e={}){e.interpolation||(e.interpolation={escapeValue:!0});const{escape:t,escapeValue:s,useRawValueToEscape:i,prefix:n,prefixEscaped:a,suffix:o,suffixEscaped:u,formatSeparator:l,unescapeSuffix:f,unescapePrefix:c,nestingPrefix:g,nestingPrefixEscaped:d,nestingSuffix:h,nestingSuffixEscaped:m,nestingOptionsSeparator:S,maxReplaces:C,alwaysFormat:b}=e.interpolation;this.escape=t!==void 0?t:Ye,this.escapeValue=s!==void 0?s:!0,this.useRawValueToEscape=i!==void 0?i:!1,this.prefix=n?K(n):a||"{{",this.suffix=o?K(o):u||"}}",this.formatSeparator=l||",",this.unescapePrefix=f?"":c||"-",this.unescapeSuffix=this.unescapePrefix?"":f||"",this.nestingPrefix=g?K(g):d||K("$t("),this.nestingSuffix=h?K(h):m||K(")"),this.nestingOptionsSeparator=S||",",this.maxReplaces=C||1e3,this.alwaysFormat=b!==void 0?b:!1,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){const e=(t,s)=>(t==null?void 0:t.source)===s?(t.lastIndex=0,t):new RegExp(s,"g");this.regexp=e(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=e(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=e(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(e,t,s,i){var d;let n,a,o;const u=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},l=h=>{if(h.indexOf(this.formatSeparator)<0){const b=Se(t,u,h,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(b,void 0,s,{...i,...t,interpolationkey:h}):b}const m=h.split(this.formatSeparator),S=m.shift().trim(),C=m.join(this.formatSeparator).trim();return this.format(Se(t,u,S,this.options.keySeparator,this.options.ignoreJSONStructure),C,s,{...i,...t,interpolationkey:S})};this.resetRegExp();const f=(i==null?void 0:i.missingInterpolationHandler)||this.options.missingInterpolationHandler,c=((d=i==null?void 0:i.interpolation)==null?void 0:d.skipOnVariables)!==void 0?i.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:h=>X(h)},{regex:this.regexp,safeValue:h=>this.escapeValue?X(this.escape(h)):X(h)}].forEach(h=>{for(o=0;n=h.regex.exec(e);){const m=n[1].trim();if(a=l(m),a===void 0)if(typeof f=="function"){const C=f(e,n,i);a=p(C)?C:""}else if(i&&Object.prototype.hasOwnProperty.call(i,m))a="";else if(c){a=n[0];continue}else this.logger.warn(`missed to pass in variable ${m} for interpolating ${e}`),a="";else!p(a)&&!this.useRawValueToEscape&&(a=ue(a));const S=h.safeValue(a);if(e=e.replace(n[0],S),c?(h.regex.lastIndex+=a.length,h.regex.lastIndex-=n[0].length):h.regex.lastIndex=0,o++,o>=this.maxReplaces)break}}),e}nest(e,t,s={}){let i,n,a;const o=(u,l)=>{const f=this.nestingOptionsSeparator;if(u.indexOf(f)<0)return u;const c=u.split(new RegExp(`${f}[ ]*{`));let g=`{${c[1]}`;u=c[0],g=this.interpolate(g,a);const d=g.match(/'/g),h=g.match(/"/g);(((d==null?void 0:d.length)??0)%2===0&&!h||h.length%2!==0)&&(g=g.replace(/'/g,'"'));try{a=JSON.parse(g),l&&(a={...l,...a})}catch(m){return this.logger.warn(`failed parsing options string in nesting for key ${u}`,m),`${u}${f}${g}`}return a.defaultValue&&a.defaultValue.indexOf(this.prefix)>-1&&delete a.defaultValue,u};for(;i=this.nestingRegexp.exec(e);){let u=[];a={...s},a=a.replace&&!p(a.replace)?a.replace:a,a.applyPostProcessor=!1,delete a.defaultValue;let l=!1;if(i[0].indexOf(this.formatSeparator)!==-1&&!/{.*}/.test(i[1])){const f=i[1].split(this.formatSeparator).map(c=>c.trim());i[1]=f.shift(),u=f,l=!0}if(n=t(o.call(this,i[1].trim(),a),a),n&&i[0]===e&&!p(n))return n;p(n)||(n=ue(n)),n||(this.logger.warn(`missed to resolve ${i[1]} for nesting ${e}`),n=""),l&&(n=u.reduce((f,c)=>this.format(f,c,s.lng,{...s,interpolationkey:i[1].trim()}),n.trim())),e=e.replace(i[0],n),this.regexp.lastIndex=0}return e}}const it=r=>{let e=r.toLowerCase().trim();const t={};if(r.indexOf("(")>-1){const s=r.split("(");e=s[0].toLowerCase().trim();const i=s[1].substring(0,s[1].length-1);e==="currency"&&i.indexOf(":")<0?t.currency||(t.currency=i.trim()):e==="relativetime"&&i.indexOf(":")<0?t.range||(t.range=i.trim()):i.split(";").forEach(a=>{if(a){const[o,...u]=a.split(":"),l=u.join(":").trim().replace(/^'+|'+$/g,""),f=o.trim();t[f]||(t[f]=l),l==="false"&&(t[f]=!1),l==="true"&&(t[f]=!0),isNaN(l)||(t[f]=parseInt(l,10))}})}return{formatName:e,formatOptions:t}},be=r=>{const e={};return(t,s,i)=>{let n=i;i&&i.interpolationkey&&i.formatParams&&i.formatParams[i.interpolationkey]&&i[i.interpolationkey]&&(n={...n,[i.interpolationkey]:void 0});const a=s+JSON.stringify(n);let o=e[a];return o||(o=r(B(s),i),e[a]=o),o(t)}},nt=r=>(e,t,s)=>r(B(t),s)(e);class rt{constructor(e={}){this.logger=j.create("formatter"),this.options=e,this.init(e)}init(e,t={interpolation:{}}){this.formatSeparator=t.interpolation.formatSeparator||",";const s=t.cacheInBuiltFormats?be:nt;this.formats={number:s((i,n)=>{const a=new Intl.NumberFormat(i,{...n});return o=>a.format(o)}),currency:s((i,n)=>{const a=new Intl.NumberFormat(i,{...n,style:"currency"});return o=>a.format(o)}),datetime:s((i,n)=>{const a=new Intl.DateTimeFormat(i,{...n});return o=>a.format(o)}),relativetime:s((i,n)=>{const a=new Intl.RelativeTimeFormat(i,{...n});return o=>a.format(o,n.range||"day")}),list:s((i,n)=>{const a=new Intl.ListFormat(i,{...n});return o=>a.format(o)})}}add(e,t){this.formats[e.toLowerCase().trim()]=t}addCached(e,t){this.formats[e.toLowerCase().trim()]=be(t)}format(e,t,s,i={}){const n=t.split(this.formatSeparator);if(n.length>1&&n[0].indexOf("(")>1&&n[0].indexOf(")")<0&&n.find(o=>o.indexOf(")")>-1)){const o=n.findIndex(u=>u.indexOf(")")>-1);n[0]=[n[0],...n.splice(1,o)].join(this.formatSeparator)}return n.reduce((o,u)=>{var c;const{formatName:l,formatOptions:f}=it(u);if(this.formats[l]){let g=o;try{const d=((c=i==null?void 0:i.formatParams)==null?void 0:c[i.interpolationkey])||{},h=d.locale||d.lng||i.locale||i.lng||s;g=this.formats[l](o,h,{...f,...i,...d})}catch(d){this.logger.warn(d)}return g}else this.logger.warn(`there was no format function for ${l}`);return o},e)}}const at=(r,e)=>{r.pending[e]!==void 0&&(delete r.pending[e],r.pendingCount--)};class ot extends Z{constructor(e,t,s,i={}){var n,a;super(),this.backend=e,this.store=t,this.services=s,this.languageUtils=s.languageUtils,this.options=i,this.logger=j.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=i.maxParallelReads||10,this.readingCalls=0,this.maxRetries=i.maxRetries>=0?i.maxRetries:5,this.retryTimeout=i.retryTimeout>=1?i.retryTimeout:350,this.state={},this.queue=[],(a=(n=this.backend)==null?void 0:n.init)==null||a.call(n,s,i.backend,i)}queueLoad(e,t,s,i){const n={},a={},o={},u={};return e.forEach(l=>{let f=!0;t.forEach(c=>{const g=`${l}|${c}`;!s.reload&&this.store.hasResourceBundle(l,c)?this.state[g]=2:this.state[g]<0||(this.state[g]===1?a[g]===void 0&&(a[g]=!0):(this.state[g]=1,f=!1,a[g]===void 0&&(a[g]=!0),n[g]===void 0&&(n[g]=!0),u[c]===void 0&&(u[c]=!0)))}),f||(o[l]=!0)}),(Object.keys(n).length||Object.keys(a).length)&&this.queue.push({pending:a,pendingCount:Object.keys(a).length,loaded:{},errors:[],callback:i}),{toLoad:Object.keys(n),pending:Object.keys(a),toLoadLanguages:Object.keys(o),toLoadNamespaces:Object.keys(u)}}loaded(e,t,s){const i=e.split("|"),n=i[0],a=i[1];t&&this.emit("failedLoading",n,a,t),!t&&s&&this.store.addResourceBundle(n,a,s,void 0,void 0,{skipCopy:!0}),this.state[e]=t?-1:2,t&&s&&(this.state[e]=0);const o={};this.queue.forEach(u=>{We(u.loaded,[n],a),at(u,e),t&&u.errors.push(t),u.pendingCount===0&&!u.done&&(Object.keys(u.loaded).forEach(l=>{o[l]||(o[l]={});const f=u.loaded[l];f.length&&f.forEach(c=>{o[l][c]===void 0&&(o[l][c]=!0)})}),u.done=!0,u.errors.length?u.callback(u.errors):u.callback())}),this.emit("loaded",o),this.queue=this.queue.filter(u=>!u.done)}read(e,t,s,i=0,n=this.retryTimeout,a){if(!e.length)return a(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:e,ns:t,fcName:s,tried:i,wait:n,callback:a});return}this.readingCalls++;const o=(l,f)=>{if(this.readingCalls--,this.waitingReads.length>0){const c=this.waitingReads.shift();this.read(c.lng,c.ns,c.fcName,c.tried,c.wait,c.callback)}if(l&&f&&i<this.maxRetries){setTimeout(()=>{this.read.call(this,e,t,s,i+1,n*2,a)},n);return}a(l,f)},u=this.backend[s].bind(this.backend);if(u.length===2){try{const l=u(e,t);l&&typeof l.then=="function"?l.then(f=>o(null,f)).catch(o):o(null,l)}catch(l){o(l)}return}return u(e,t,o)}prepareLoading(e,t,s={},i){if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),i&&i();p(e)&&(e=this.languageUtils.toResolveHierarchy(e)),p(t)&&(t=[t]);const n=this.queueLoad(e,t,s,i);if(!n.toLoad.length)return n.pending.length||i(),null;n.toLoad.forEach(a=>{this.loadOne(a)})}load(e,t,s){this.prepareLoading(e,t,{},s)}reload(e,t,s){this.prepareLoading(e,t,{reload:!0},s)}loadOne(e,t=""){const s=e.split("|"),i=s[0],n=s[1];this.read(i,n,"read",void 0,void 0,(a,o)=>{a&&this.logger.warn(`${t}loading namespace ${n} for language ${i} failed`,a),!a&&o&&this.logger.log(`${t}loaded namespace ${n} for language ${i}`,o),this.loaded(e,a,o)})}saveMissing(e,t,s,i,n,a={},o=()=>{}){var u,l,f,c,g;if((l=(u=this.services)==null?void 0:u.utils)!=null&&l.hasLoadedNamespace&&!((c=(f=this.services)==null?void 0:f.utils)!=null&&c.hasLoadedNamespace(t))){this.logger.warn(`did not save key "${s}" as the namespace "${t}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(!(s==null||s==="")){if((g=this.backend)!=null&&g.create){const d={...a,isUpdate:n},h=this.backend.create.bind(this.backend);if(h.length<6)try{let m;h.length===5?m=h(e,t,s,i,d):m=h(e,t,s,i),m&&typeof m.then=="function"?m.then(S=>o(null,S)).catch(o):o(null,m)}catch(m){o(m)}else h(e,t,s,i,o,d)}!e||!e[0]||this.store.addResource(e[0],t,s,i)}}}const Oe=()=>({debug:!1,initAsync:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:r=>{let e={};if(typeof r[1]=="object"&&(e=r[1]),p(r[1])&&(e.defaultValue=r[1]),p(r[2])&&(e.tDescription=r[2]),typeof r[2]=="object"||typeof r[3]=="object"){const t=r[3]||r[2];Object.keys(t).forEach(s=>{e[s]=t[s]})}return e},interpolation:{escapeValue:!0,format:r=>r,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0},cacheInBuiltFormats:!0}),Le=r=>{var e,t;return p(r.ns)&&(r.ns=[r.ns]),p(r.fallbackLng)&&(r.fallbackLng=[r.fallbackLng]),p(r.fallbackNS)&&(r.fallbackNS=[r.fallbackNS]),((t=(e=r.supportedLngs)==null?void 0:e.indexOf)==null?void 0:t.call(e,"cimode"))<0&&(r.supportedLngs=r.supportedLngs.concat(["cimode"])),typeof r.initImmediate=="boolean"&&(r.initAsync=r.initImmediate),r},_=()=>{},lt=r=>{Object.getOwnPropertyNames(Object.getPrototypeOf(r)).forEach(t=>{typeof r[t]=="function"&&(r[t]=r[t].bind(r))})};class J extends Z{constructor(e={},t){if(super(),this.options=Le(e),this.services={},this.logger=j,this.modules={external:[]},lt(this),t&&!this.isInitialized&&!e.isClone){if(!this.options.initAsync)return this.init(e,t),this;setTimeout(()=>{this.init(e,t)},0)}}init(e={},t){this.isInitializing=!0,typeof e=="function"&&(t=e,e={}),e.defaultNS==null&&e.ns&&(p(e.ns)?e.defaultNS=e.ns:e.ns.indexOf("translation")<0&&(e.defaultNS=e.ns[0]));const s=Oe();this.options={...s,...this.options,...Le(e)},this.options.interpolation={...s.interpolation,...this.options.interpolation},e.keySeparator!==void 0&&(this.options.userDefinedKeySeparator=e.keySeparator),e.nsSeparator!==void 0&&(this.options.userDefinedNsSeparator=e.nsSeparator);const i=l=>l?typeof l=="function"?new l:l:null;if(!this.options.isClone){this.modules.logger?j.init(i(this.modules.logger),this.options):j.init(null,this.options);let l;this.modules.formatter?l=this.modules.formatter:l=rt;const f=new me(this.options);this.store=new de(this.options.resources,this.options);const c=this.services;c.logger=j,c.resourceStore=this.store,c.languageUtils=f,c.pluralResolver=new tt(f,{prepend:this.options.pluralSeparator,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),l&&(!this.options.interpolation.format||this.options.interpolation.format===s.interpolation.format)&&(c.formatter=i(l),c.formatter.init(c,this.options),this.options.interpolation.format=c.formatter.format.bind(c.formatter)),c.interpolator=new st(this.options),c.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},c.backendConnector=new ot(i(this.modules.backend),c.resourceStore,c,this.options),c.backendConnector.on("*",(g,...d)=>{this.emit(g,...d)}),this.modules.languageDetector&&(c.languageDetector=i(this.modules.languageDetector),c.languageDetector.init&&c.languageDetector.init(c,this.options.detection,this.options)),this.modules.i18nFormat&&(c.i18nFormat=i(this.modules.i18nFormat),c.i18nFormat.init&&c.i18nFormat.init(this)),this.translator=new Q(this.services,this.options),this.translator.on("*",(g,...d)=>{this.emit(g,...d)}),this.modules.external.forEach(g=>{g.init&&g.init(this)})}if(this.format=this.options.interpolation.format,t||(t=_),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){const l=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);l.length>0&&l[0]!=="dev"&&(this.options.lng=l[0])}!this.services.languageDetector&&!this.options.lng&&this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(l=>{this[l]=(...f)=>this.store[l](...f)}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(l=>{this[l]=(...f)=>(this.store[l](...f),this)});const o=H(),u=()=>{const l=(f,c)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),o.resolve(c),t(f,c)};if(this.languages&&!this.isInitialized)return l(null,this.t.bind(this));this.changeLanguage(this.options.lng,l)};return this.options.resources||!this.options.initAsync?u():setTimeout(u,0),o}loadResources(e,t=_){var n,a;let s=t;const i=p(e)?e:this.language;if(typeof e=="function"&&(s=e),!this.options.resources||this.options.partialBundledLanguages){if((i==null?void 0:i.toLowerCase())==="cimode"&&(!this.options.preload||this.options.preload.length===0))return s();const o=[],u=l=>{if(!l||l==="cimode")return;this.services.languageUtils.toResolveHierarchy(l).forEach(c=>{c!=="cimode"&&o.indexOf(c)<0&&o.push(c)})};i?u(i):this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(f=>u(f)),(a=(n=this.options.preload)==null?void 0:n.forEach)==null||a.call(n,l=>u(l)),this.services.backendConnector.load(o,this.options.ns,l=>{!l&&!this.resolvedLanguage&&this.language&&this.setResolvedLanguage(this.language),s(l)})}else s(null)}reloadResources(e,t,s){const i=H();return typeof e=="function"&&(s=e,e=void 0),typeof t=="function"&&(s=t,t=void 0),e||(e=this.languages),t||(t=this.options.ns),s||(s=_),this.services.backendConnector.reload(e,t,n=>{i.resolve(),s(n)}),i}use(e){if(!e)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return e.type==="backend"&&(this.modules.backend=e),(e.type==="logger"||e.log&&e.warn&&e.error)&&(this.modules.logger=e),e.type==="languageDetector"&&(this.modules.languageDetector=e),e.type==="i18nFormat"&&(this.modules.i18nFormat=e),e.type==="postProcessor"&&ve.addPostProcessor(e),e.type==="formatter"&&(this.modules.formatter=e),e.type==="3rdParty"&&this.modules.external.push(e),this}setResolvedLanguage(e){if(!(!e||!this.languages)&&!(["cimode","dev"].indexOf(e)>-1)){for(let t=0;t<this.languages.length;t++){const s=this.languages[t];if(!(["cimode","dev"].indexOf(s)>-1)&&this.store.hasLanguageSomeTranslations(s)){this.resolvedLanguage=s;break}}!this.resolvedLanguage&&this.languages.indexOf(e)<0&&this.store.hasLanguageSomeTranslations(e)&&(this.resolvedLanguage=e,this.languages.unshift(e))}}changeLanguage(e,t){this.isLanguageChangingTo=e;const s=H();this.emit("languageChanging",e);const i=o=>{this.language=o,this.languages=this.services.languageUtils.toResolveHierarchy(o),this.resolvedLanguage=void 0,this.setResolvedLanguage(o)},n=(o,u)=>{u?this.isLanguageChangingTo===e&&(i(u),this.translator.changeLanguage(u),this.isLanguageChangingTo=void 0,this.emit("languageChanged",u),this.logger.log("languageChanged",u)):this.isLanguageChangingTo=void 0,s.resolve((...l)=>this.t(...l)),t&&t(o,(...l)=>this.t(...l))},a=o=>{var f,c;!e&&!o&&this.services.languageDetector&&(o=[]);const u=p(o)?o:o&&o[0],l=this.store.hasLanguageSomeTranslations(u)?u:this.services.languageUtils.getBestMatchFromCodes(p(o)?[o]:o);l&&(this.language||i(l),this.translator.language||this.translator.changeLanguage(l),(c=(f=this.services.languageDetector)==null?void 0:f.cacheUserLanguage)==null||c.call(f,l)),this.loadResources(l,g=>{n(g,l)})};return!e&&this.services.languageDetector&&!this.services.languageDetector.async?a(this.services.languageDetector.detect()):!e&&this.services.languageDetector&&this.services.languageDetector.async?this.services.languageDetector.detect.length===0?this.services.languageDetector.detect().then(a):this.services.languageDetector.detect(a):a(e),s}getFixedT(e,t,s){const i=(n,a,...o)=>{let u;typeof a!="object"?u=this.options.overloadTranslationOptionHandler([n,a].concat(o)):u={...a},u.lng=u.lng||i.lng,u.lngs=u.lngs||i.lngs,u.ns=u.ns||i.ns,u.keyPrefix!==""&&(u.keyPrefix=u.keyPrefix||s||i.keyPrefix);const l=this.options.keySeparator||".";let f;return u.keyPrefix&&Array.isArray(n)?f=n.map(c=>`${u.keyPrefix}${l}${c}`):f=u.keyPrefix?`${u.keyPrefix}${l}${n}`:n,this.t(f,u)};return p(e)?i.lng=e:i.lngs=e,i.ns=t,i.keyPrefix=s,i}t(...e){var t;return(t=this.translator)==null?void 0:t.translate(...e)}exists(...e){var t;return(t=this.translator)==null?void 0:t.exists(...e)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e,t={}){if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;const s=t.lng||this.resolvedLanguage||this.languages[0],i=this.options?this.options.fallbackLng:!1,n=this.languages[this.languages.length-1];if(s.toLowerCase()==="cimode")return!0;const a=(o,u)=>{const l=this.services.backendConnector.state[`${o}|${u}`];return l===-1||l===0||l===2};if(t.precheck){const o=t.precheck(this,a);if(o!==void 0)return o}return!!(this.hasResourceBundle(s,e)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||a(s,e)&&(!i||a(n,e)))}loadNamespaces(e,t){const s=H();return this.options.ns?(p(e)&&(e=[e]),e.forEach(i=>{this.options.ns.indexOf(i)<0&&this.options.ns.push(i)}),this.loadResources(i=>{s.resolve(),t&&t(i)}),s):(t&&t(),Promise.resolve())}loadLanguages(e,t){const s=H();p(e)&&(e=[e]);const i=this.options.preload||[],n=e.filter(a=>i.indexOf(a)<0&&this.services.languageUtils.isSupportedCode(a));return n.length?(this.options.preload=i.concat(n),this.loadResources(a=>{s.resolve(),t&&t(a)}),s):(t&&t(),Promise.resolve())}dir(e){var i,n;if(e||(e=this.resolvedLanguage||(((i=this.languages)==null?void 0:i.length)>0?this.languages[0]:this.language)),!e)return"rtl";const t=["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"],s=((n=this.services)==null?void 0:n.languageUtils)||new me(Oe());return t.indexOf(s.getLanguagePartFromCode(e))>-1||e.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(e={},t){return new J(e,t)}cloneInstance(e={},t=_){const s=e.forkResourceStore;s&&delete e.forkResourceStore;const i={...this.options,...e,isClone:!0},n=new J(i);if((e.debug!==void 0||e.prefix!==void 0)&&(n.logger=n.logger.clone(e)),["store","services","language"].forEach(o=>{n[o]=this[o]}),n.services={...this.services},n.services.utils={hasLoadedNamespace:n.hasLoadedNamespace.bind(n)},s){const o=Object.keys(this.store.data).reduce((u,l)=>(u[l]={...this.store.data[l]},u[l]=Object.keys(u[l]).reduce((f,c)=>(f[c]={...u[l][c]},f),u[l]),u),{});n.store=new de(o,i),n.services.resourceStore=n.store}return n.translator=new Q(n.services,i),n.translator.on("*",(o,...u)=>{n.emit(o,...u)}),n.init(i,t),n.translator.options=i,n.translator.backendConnector.services.utils={hasLoadedNamespace:n.hasLoadedNamespace.bind(n)},n}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}const w=J.createInstance();w.createInstance=J.createInstance;w.createInstance;w.dir;w.init;w.loadResources;w.reloadResources;w.use;w.changeLanguage;w.getFixedT;w.t;w.exists;w.setDefaultNamespace;w.hasLoadedNamespace;w.loadNamespaces;w.loadLanguages;export{ft as a,w as i,ct as u};
