import  { useState, useMemo } from 'react'
import { useQuery } from '@tanstack/react-query'
import { FilteredDropdown, type DropdownOption } from '@/components/ui/filtered-dropdown'

// Sample data similar to your screenshot
const sampleUsers: DropdownOption[] = [
  { value: '62305', label: 'КАЛИНА БЪРЗАШКА, 62305', group: 'Най-често избирани' },
  { value: '61879', label: 'ВИКТОР АСЕНОВ, 61879', group: 'Софтуерно Инженерство' },
  { value: '62249', label: 'ГЕОРГИ МАРКОВ, 62249', group: 'Софтуерно Инженерство' },
  { value: '62663', label: 'ГЕРГАНА СТРАЖАКОВА, 62663', group: 'Софтуерно Инженерство' },
  { value: '56381', label: 'ДЕСИСЛАВА ТОДОРОВА, 56381', group: 'Софтуерно Инженерство' },
  { value: '61051', label: 'ДИМИТЪР ДИМИТРОВ, 61051', group: 'Софтуерно Инженерство' },
  { value: '62305-2', label: 'КАЛИНА БЪРЗАШКА, 62305', group: 'Софтуерно Инженерство' },
]

const cities: DropdownOption[] = [
  { value: 'sofia', label: 'София', group: 'Големи градове' },
  { value: 'plovdiv', label: 'Пловдив', group: 'Големи градове' },
  { value: 'varna', label: 'Варна', group: 'Големи градове' },
  { value: 'burgas', label: 'Бургас', group: 'Големи градове' },
  { value: 'ruse', label: 'Русе', group: 'Средни градове' },
  { value: 'stara-zagora', label: 'Стара Загора', group: 'Средни градове' },
  { value: 'pleven', label: 'Плевен', group: 'Средни градове' },
]

// Mock API function
const searchUsers = async (query: string): Promise<DropdownOption[]> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500))
  
  // Filter based on query
  return sampleUsers.filter(user => 
    user.label.toLowerCase().includes(query.toLowerCase())
  )
}

export function FilteredDropdownExamples() {
  const [selectedUser, setSelectedUser] = useState<string>('')
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])
  const [selectedCity, setSelectedCity] = useState<string>('')
  const [apiSelectedUser, setApiSelectedUser] = useState<string>('')

  return (
    <div className="space-y-8 p-6 max-w-2xl">
      <h1 className="text-2xl font-bold">Filtered Dropdown Examples</h1>
      
      {/* Basic Single Select with Internal Filtering */}
      <div className="space-y-2">
        <label className="text-sm font-medium">Single Select with Internal Filtering</label>
        <FilteredDropdown
          options={sampleUsers}
          value={selectedUser}
          onValueChange={(value) => setSelectedUser(value as string)}
          placeholder="Най-често избирани..."
          groupBy="group"
          groupLabels={{
            'Най-често избирани': 'Най-често избирани',
            'Софтуерно Инженерство': 'Софтуерно Инженерство'
          }}
          clearable
        />
        <p className="text-sm text-muted-foreground">Selected: {selectedUser}</p>
      </div>

      {/* Multi-Select */}
      <div className="space-y-2">
        <label className="text-sm font-medium">Multi-Select</label>
        <FilteredDropdown
          options={sampleUsers}
          value={selectedUsers}
          onValueChange={(value) => setSelectedUsers(value as string[])}
          placeholder="Select multiple users..."
          multiple
          maxSelected={3}
          groupBy="group"
          clearable
        />
        <p className="text-sm text-muted-foreground">
          Selected: {selectedUsers.join(', ')}
        </p>
      </div>

      {/* Grouped Options */}
      <div className="space-y-2">
        <label className="text-sm font-medium">Grouped Cities</label>
        <FilteredDropdown
          options={cities}
          value={selectedCity}
          onValueChange={(value) => setSelectedCity(value as string)}
          placeholder="Select city..."
          groupBy="group"
          groupLabels={{
            'Големи градове': 'Големи градове',
            'Средни градове': 'Средни градове'
          }}
          clearable
        />
        <p className="text-sm text-muted-foreground">Selected: {selectedCity}</p>
      </div>

      {/* External Filtering */}
      <div className="space-y-2">
        <label className="text-sm font-medium">External Filtering</label>
        <FilteredDropdown
        filterType="external"
        options={sampleUsers}
        value={selectedUser}
        onValueChange={(value) => setSelectedUser(value as string)}
        placeholder="Select user..."
        groupBy="group"
        clearable
      />
      </div>

      {/* API-based Filtering */}
      <div className="space-y-2">
        <label className="text-sm font-medium">API-based Filtering</label>
        <FilteredDropdown
          filterType="api"
          onSearch={searchUsers}
          value={apiSelectedUser}
          onValueChange={(value) => setApiSelectedUser(value as string)}
          placeholder="Type to search users..."
          searchPlaceholder="Search users..."
          minSearchLength={2}
          searchDebounceMs={300}
          groupBy="group"
          clearable

        />
        <p className="text-sm text-muted-foreground">Selected: {apiSelectedUser}</p>
      </div>

      {/* Custom Rendering */}
      <div className="space-y-2">
        <label className="text-sm font-medium">Custom Option Rendering</label>
        <FilteredDropdown
          options={sampleUsers}
          value={selectedUser}
          onValueChange={(value) => setSelectedUser(value as string)}
          placeholder="Select user..."
          renderOption={(option) => (
            <div className="flex flex-col">
              <span className="font-medium">{option.label.split(',')[0]}</span>
              <span className="text-xs text-muted-foreground">
                ID: {option.label.split(',')[1]?.trim()}
              </span>
            </div>
          )}
          displayValue={(option) => option.label.split(',')[0]}
          groupBy="group"
        />
      </div>

      {/* Disabled State */}
      <div className="space-y-2">
        <label className="text-sm font-medium">Disabled Dropdown</label>
        <FilteredDropdown
          options={cities}
          value=""
          onValueChange={() => {}}
          placeholder="This is disabled..."
          disabled
        />
      </div>

      {/* No Search */}
      <div className="space-y-2">
        <label className="text-sm font-medium">Without Search</label>
        <FilteredDropdown
          options={cities.slice(0, 4)}
          value={selectedCity}
          onValueChange={(value) => setSelectedCity(value as string)}
          placeholder="Select city..."
          showSearch={false}
        />
      </div>
    </div>
  )
}

// Usage examples for different scenarios:

// 1. Simple dropdown with static data
export function SimpleDropdownExample() {
  const [value, setValue] = useState('')
  
  return (
    <FilteredDropdown
      options={[
        { value: 'option1', label: 'Option 1' },
        { value: 'option2', label: 'Option 2' },
        { value: 'option3', label: 'Option 3' },
      ]}
      value={value}
      onValueChange={(val) => setValue(val as string)}
      placeholder="Choose an option..."
    />
  )
}

// 2. API-driven dropdown
export function ApiDropdownExample() {
  const [value, setValue] = useState('')
  
  const searchApi = async (query: string) => {
    const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`)
    const data = await response.json()
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return data.map((item: any) => ({
      value: item.id,
      label: item.name,
      group: item.category
    }))
  }
  
  return (
    <FilteredDropdown
      filterType="api"
      onSearch={searchApi}
      value={value}
      onValueChange={(val) => setValue(val as string)}
      placeholder="Type to search..."
      minSearchLength={2}
      groupBy="group"
    />
  )
}

// 3. Multi-select with grouping
export function MultiSelectGroupedExample() {
  const [values, setValues] = useState<string[]>([])
  
  return (
    <FilteredDropdown
      options={sampleUsers}
      value={values}
      onValueChange={(vals) => setValues(vals as string[])}
      multiple
      maxSelected={5}
      groupBy="group"
      placeholder="Select multiple users..."
      clearable
    />
  )
}

// Example with useQuery integration (External filtering)
export function UseQueryExample() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedTechnician, setSelectedTechnician] = useState("")

  // Mock API function - replace with your actual API
  const fetchTechnicians = async (query: string) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500))

    // Mock data - replace with actual API call
    const mockData = [
      { displayName: 'КАЛИНА БЪРЗАШКА', iptuName: 'ИПТУ София', eln: '62305' },
      { displayName: 'ВИКТОР АСЕНОВ', iptuName: 'ИПТУ Пловдив', eln: '61879' },
      { displayName: 'ГЕОРГИ МАРКОВ', iptuName: 'ИПТУ Варна', eln: '62249' },
      { displayName: 'ГЕРГАНА СТРАЖАКОВА', iptuName: 'ИПТУ София', eln: '62663' },
    ]

    return mockData.filter(tech =>
      tech.displayName.toLowerCase().includes(query.toLowerCase())
    )
  }

  // useQuery hook for fetching data
  const { data: technicianData, isLoading } = useQuery({
    queryKey: ['technicians', searchQuery],
    queryFn: () => fetchTechnicians(searchQuery),
    enabled: searchQuery.length >= 2, // Only search when query is at least 2 characters
    staleTime: 30000, // Cache for 30 seconds
  })

  // Convert API data to DropdownOption format
  const dropdownOptions: DropdownOption[] = useMemo(() => {
    if (!technicianData) return []

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return technicianData.map((tech: any) => ({
      value: tech.displayName,
      label: `${tech.displayName} - ${tech.iptuName} - ${tech.eln}`,
      group: tech.iptuName,
      originalData: tech
    }))
  }, [technicianData])

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">useQuery Integration Example</h3>
      <p className="text-sm text-muted-foreground">
        This example shows how to use FilteredDropdown with useQuery for external data fetching.
        Type at least 2 characters to search.
      </p>

      <FilteredDropdown
        filterType="external"
        options={dropdownOptions}
        value={selectedTechnician}
        onValueChange={(value) => setSelectedTechnician(Array.isArray(value) ? value[0] : value)}
        placeholder="Search technicians..."
        searchPlaceholder="Type to search technicians..."
        emptyText="No technicians found"
        loadingText="Searching..."
        minSearchLength={2}
        searchDebounceMs={1000} // 1 second debounce for demo
        groupBy="group"
        clearable
        loading={isLoading}
        searchQuery={searchQuery}
        onSearchQueryChange={setSearchQuery}
      />

      <p className="text-sm text-muted-foreground">
        Selected: {selectedTechnician || 'None'}
      </p>
      <p className="text-xs text-muted-foreground">
        Search query: "{searchQuery}" {isLoading && '(Loading...)'}
      </p>
      <p className="text-xs text-blue-600">
        💡 Tip: Notice the 1-second debounce delay before the search query triggers the API call
      </p>
    </div>
  )
}
