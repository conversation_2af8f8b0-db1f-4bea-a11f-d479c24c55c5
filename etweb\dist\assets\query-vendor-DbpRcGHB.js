var ve=e=>{throw TypeError(e)};var Wt=(e,t,s)=>t.has(e)||ve("Cannot "+s);var i=(e,t,s)=>(Wt(e,t,"read from private field"),s?s.call(e):t.get(e)),c=(e,t,s)=>t.has(e)?ve("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,s),u=(e,t,s,r)=>(Wt(e,t,"write to private field"),r?r.call(e,s):t.set(e,s),s),v=(e,t,s)=>(Wt(e,t,"access private method"),s);var Nt=(e,t,s,r)=>({set _(n){u(e,t,n,s)},get _(){return i(e,t,r)}});import{r as M}from"./router-vendor-CX6yTN5-.js";var Yt={exports:{}},jt={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var me;function Xe(){if(me)return jt;me=1;var e=Symbol.for("react.transitional.element"),t=Symbol.for("react.fragment");function s(r,n,a){var o=null;if(a!==void 0&&(o=""+a),n.key!==void 0&&(o=""+n.key),"key"in n){a={};for(var h in n)h!=="key"&&(a[h]=n[h])}else a=n;return n=a.ref,{$$typeof:e,type:r,key:o,ref:n!==void 0?n:null,props:a}}return jt.Fragment=t,jt.jsx=s,jt.jsxs=s,jt}var be;function Ze(){return be||(be=1,Yt.exports=Xe()),Yt.exports}var ts=Ze(),It=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},gt=typeof window>"u"||"Deno"in globalThis;function U(){}function es(e,t){return typeof e=="function"?e(t):e}function Zt(e){return typeof e=="number"&&e>=0&&e!==1/0}function ke(e,t){return Math.max(e+(t||0)-Date.now(),0)}function ht(e,t){return typeof e=="function"?e(t):e}function B(e,t){return typeof e=="function"?e(t):e}function ge(e,t){const{type:s="all",exact:r,fetchStatus:n,predicate:a,queryKey:o,stale:h}=e;if(o){if(r){if(t.queryHash!==de(o,t.options))return!1}else if(!Lt(t.queryKey,o))return!1}if(s!=="all"){const d=t.isActive();if(s==="active"&&!d||s==="inactive"&&d)return!1}return!(typeof h=="boolean"&&t.isStale()!==h||n&&n!==t.state.fetchStatus||a&&!a(t))}function Ce(e,t){const{exact:s,status:r,predicate:n,mutationKey:a}=e;if(a){if(!t.options.mutationKey)return!1;if(s){if(Ct(t.options.mutationKey)!==Ct(a))return!1}else if(!Lt(t.options.mutationKey,a))return!1}return!(r&&t.state.status!==r||n&&!n(t))}function de(e,t){return((t==null?void 0:t.queryKeyHashFn)||Ct)(e)}function Ct(e){return JSON.stringify(e,(t,s)=>te(s)?Object.keys(s).sort().reduce((r,n)=>(r[n]=s[n],r),{}):s)}function Lt(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?Object.keys(t).every(s=>Lt(e[s],t[s])):!1}function Le(e,t){if(e===t)return e;const s=Re(e)&&Re(t);if(s||te(e)&&te(t)){const r=s?e:Object.keys(e),n=r.length,a=s?t:Object.keys(t),o=a.length,h=s?[]:{},d=new Set(r);let g=0;for(let R=0;R<o;R++){const l=s?R:a[R];(!s&&d.has(l)||s)&&e[l]===void 0&&t[l]===void 0?(h[l]=void 0,g++):(h[l]=Le(e[l],t[l]),h[l]===e[l]&&e[l]!==void 0&&g++)}return n===o&&g===n?e:h}return t}function $t(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(e[s]!==t[s])return!1;return!0}function Re(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function te(e){if(!Oe(e))return!1;const t=e.constructor;if(t===void 0)return!0;const s=t.prototype;return!(!Oe(s)||!s.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(e)!==Object.prototype)}function Oe(e){return Object.prototype.toString.call(e)==="[object Object]"}function ss(e){return new Promise(t=>{setTimeout(t,e)})}function ee(e,t,s){return typeof s.structuralSharing=="function"?s.structuralSharing(e,t):s.structuralSharing!==!1?Le(e,t):t}function is(e,t,s=0){const r=[...e,t];return s&&r.length>s?r.slice(1):r}function rs(e,t,s=0){const r=[t,...e];return s&&r.length>s?r.slice(0,-1):r}var fe=Symbol();function _e(e,t){return!e.queryFn&&(t!=null&&t.initialPromise)?()=>t.initialPromise:!e.queryFn||e.queryFn===fe?()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`)):e.queryFn}function Ke(e,t){return typeof e=="function"?e(...t):!!e}var lt,Z,Ot,Qe,ns=(Qe=class extends It{constructor(){super();c(this,lt);c(this,Z);c(this,Ot);u(this,Ot,t=>{if(!gt&&window.addEventListener){const s=()=>t();return window.addEventListener("visibilitychange",s,!1),()=>{window.removeEventListener("visibilitychange",s)}}})}onSubscribe(){i(this,Z)||this.setEventListener(i(this,Ot))}onUnsubscribe(){var t;this.hasListeners()||((t=i(this,Z))==null||t.call(this),u(this,Z,void 0))}setEventListener(t){var s;u(this,Ot,t),(s=i(this,Z))==null||s.call(this),u(this,Z,t(r=>{typeof r=="boolean"?this.setFocused(r):this.onFocus()}))}setFocused(t){i(this,lt)!==t&&(u(this,lt,t),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(s=>{s(t)})}isFocused(){var t;return typeof i(this,lt)=="boolean"?i(this,lt):((t=globalThis.document)==null?void 0:t.visibilityState)!=="hidden"}},lt=new WeakMap,Z=new WeakMap,Ot=new WeakMap,Qe),ye=new ns,wt,tt,St,xe,as=(xe=class extends It{constructor(){super();c(this,wt,!0);c(this,tt);c(this,St);u(this,St,t=>{if(!gt&&window.addEventListener){const s=()=>t(!0),r=()=>t(!1);return window.addEventListener("online",s,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",s),window.removeEventListener("offline",r)}}})}onSubscribe(){i(this,tt)||this.setEventListener(i(this,St))}onUnsubscribe(){var t;this.hasListeners()||((t=i(this,tt))==null||t.call(this),u(this,tt,void 0))}setEventListener(t){var s;u(this,St,t),(s=i(this,tt))==null||s.call(this),u(this,tt,t(this.setOnline.bind(this)))}setOnline(t){i(this,wt)!==t&&(u(this,wt,t),this.listeners.forEach(r=>{r(t)}))}isOnline(){return i(this,wt)}},wt=new WeakMap,tt=new WeakMap,St=new WeakMap,xe),Vt=new as;function se(){let e,t;const s=new Promise((n,a)=>{e=n,t=a});s.status="pending",s.catch(()=>{});function r(n){Object.assign(s,n),delete s.resolve,delete s.reject}return s.resolve=n=>{r({status:"fulfilled",value:n}),e(n)},s.reject=n=>{r({status:"rejected",reason:n}),t(n)},s}function us(e){return Math.min(1e3*2**e,3e4)}function He(e){return(e??"online")==="online"?Vt.isOnline():!0}var Ge=class extends Error{constructor(e){super("CancelledError"),this.revert=e==null?void 0:e.revert,this.silent=e==null?void 0:e.silent}};function Xt(e){return e instanceof Ge}function Be(e){let t=!1,s=0,r=!1,n;const a=se(),o=f=>{var y;r||(p(new Ge(f)),(y=e.abort)==null||y.call(e))},h=()=>{t=!0},d=()=>{t=!1},g=()=>ye.isFocused()&&(e.networkMode==="always"||Vt.isOnline())&&e.canRun(),R=()=>He(e.networkMode)&&e.canRun(),l=f=>{var y;r||(r=!0,(y=e.onSuccess)==null||y.call(e,f),n==null||n(),a.resolve(f))},p=f=>{var y;r||(r=!0,(y=e.onError)==null||y.call(e,f),n==null||n(),a.reject(f))},m=()=>new Promise(f=>{var y;n=P=>{(r||g())&&f(P)},(y=e.onPause)==null||y.call(e)}).then(()=>{var f;n=void 0,r||(f=e.onContinue)==null||f.call(e)}),O=()=>{if(r)return;let f;const y=s===0?e.initialPromise:void 0;try{f=y??e.fn()}catch(P){f=Promise.reject(P)}Promise.resolve(f).then(l).catch(P=>{var K;if(r)return;const T=e.retry??(gt?0:3),w=e.retryDelay??us,x=typeof w=="function"?w(s,P):w,L=T===!0||typeof T=="number"&&s<T||typeof T=="function"&&T(s,P);if(t||!L){p(P);return}s++,(K=e.onFail)==null||K.call(e,s,P),ss(x).then(()=>g()?void 0:m()).then(()=>{t?p(P):O()})})};return{promise:a,cancel:o,continue:()=>(n==null||n(),a),cancelRetry:h,continueRetry:d,canStart:R,start:()=>(R()?O():m().then(O),a)}}var os=e=>setTimeout(e,0);function hs(){let e=[],t=0,s=h=>{h()},r=h=>{h()},n=os;const a=h=>{t?e.push(h):n(()=>{s(h)})},o=()=>{const h=e;e=[],h.length&&n(()=>{r(()=>{h.forEach(d=>{s(d)})})})};return{batch:h=>{let d;t++;try{d=h()}finally{t--,t||o()}return d},batchCalls:h=>(...d)=>{a(()=>{h(...d)})},schedule:a,setNotifyFunction:h=>{s=h},setBatchNotifyFunction:h=>{r=h},setScheduler:h=>{n=h}}}var Q=hs(),dt,Me,Ne=(Me=class{constructor(){c(this,dt)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),Zt(this.gcTime)&&u(this,dt,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(gt?1/0:5*60*1e3))}clearGcTimeout(){i(this,dt)&&(clearTimeout(i(this,dt)),u(this,dt,void 0))}},dt=new WeakMap,Me),Pt,Ft,_,ft,D,_t,yt,H,$,De,cs=(De=class extends Ne{constructor(t){super();c(this,H);c(this,Pt);c(this,Ft);c(this,_);c(this,ft);c(this,D);c(this,_t);c(this,yt);u(this,yt,!1),u(this,_t,t.defaultOptions),this.setOptions(t.options),this.observers=[],u(this,ft,t.client),u(this,_,i(this,ft).getQueryCache()),this.queryKey=t.queryKey,this.queryHash=t.queryHash,u(this,Pt,ls(this.options)),this.state=t.state??i(this,Pt),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var t;return(t=i(this,D))==null?void 0:t.promise}setOptions(t){this.options={...i(this,_t),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&i(this,_).remove(this)}setData(t,s){const r=ee(this.state.data,t,this.options);return v(this,H,$).call(this,{data:r,type:"success",dataUpdatedAt:s==null?void 0:s.updatedAt,manual:s==null?void 0:s.manual}),r}setState(t,s){v(this,H,$).call(this,{type:"setState",state:t,setStateOptions:s})}cancel(t){var r,n;const s=(r=i(this,D))==null?void 0:r.promise;return(n=i(this,D))==null||n.cancel(t),s?s.then(U).catch(U):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(i(this,Pt))}isActive(){return this.observers.some(t=>B(t.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===fe||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0?this.observers.some(t=>ht(t.options.staleTime,this)==="static"):!1}isStale(){return this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):this.state.data===void 0||this.state.isInvalidated}isStaleByTime(t=0){return this.state.data===void 0?!0:t==="static"?!1:this.state.isInvalidated?!0:!ke(this.state.dataUpdatedAt,t)}onFocus(){var s;const t=this.observers.find(r=>r.shouldFetchOnWindowFocus());t==null||t.refetch({cancelRefetch:!1}),(s=i(this,D))==null||s.continue()}onOnline(){var s;const t=this.observers.find(r=>r.shouldFetchOnReconnect());t==null||t.refetch({cancelRefetch:!1}),(s=i(this,D))==null||s.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),i(this,_).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(s=>s!==t),this.observers.length||(i(this,D)&&(i(this,yt)?i(this,D).cancel({revert:!0}):i(this,D).cancelRetry()),this.scheduleGc()),i(this,_).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||v(this,H,$).call(this,{type:"invalidate"})}fetch(t,s){var g,R,l;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(s!=null&&s.cancelRefetch))this.cancel({silent:!0});else if(i(this,D))return i(this,D).continueRetry(),i(this,D).promise}if(t&&this.setOptions(t),!this.options.queryFn){const p=this.observers.find(m=>m.options.queryFn);p&&this.setOptions(p.options)}const r=new AbortController,n=p=>{Object.defineProperty(p,"signal",{enumerable:!0,get:()=>(u(this,yt,!0),r.signal)})},a=()=>{const p=_e(this.options,s),O=(()=>{const f={client:i(this,ft),queryKey:this.queryKey,meta:this.meta};return n(f),f})();return u(this,yt,!1),this.options.persister?this.options.persister(p,O,this):p(O)},h=(()=>{const p={fetchOptions:s,options:this.options,queryKey:this.queryKey,client:i(this,ft),state:this.state,fetchFn:a};return n(p),p})();(g=this.options.behavior)==null||g.onFetch(h,this),u(this,Ft,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((R=h.fetchOptions)==null?void 0:R.meta))&&v(this,H,$).call(this,{type:"fetch",meta:(l=h.fetchOptions)==null?void 0:l.meta});const d=p=>{var m,O,f,y;Xt(p)&&p.silent||v(this,H,$).call(this,{type:"error",error:p}),Xt(p)||((O=(m=i(this,_).config).onError)==null||O.call(m,p,this),(y=(f=i(this,_).config).onSettled)==null||y.call(f,this.state.data,p,this)),this.scheduleGc()};return u(this,D,Be({initialPromise:s==null?void 0:s.initialPromise,fn:h.fetchFn,abort:r.abort.bind(r),onSuccess:p=>{var m,O,f,y;if(p===void 0){d(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(p)}catch(P){d(P);return}(O=(m=i(this,_).config).onSuccess)==null||O.call(m,p,this),(y=(f=i(this,_).config).onSettled)==null||y.call(f,p,this.state.error,this),this.scheduleGc()},onError:d,onFail:(p,m)=>{v(this,H,$).call(this,{type:"failed",failureCount:p,error:m})},onPause:()=>{v(this,H,$).call(this,{type:"pause"})},onContinue:()=>{v(this,H,$).call(this,{type:"continue"})},retry:h.options.retry,retryDelay:h.options.retryDelay,networkMode:h.options.networkMode,canRun:()=>!0})),i(this,D).start()}},Pt=new WeakMap,Ft=new WeakMap,_=new WeakMap,ft=new WeakMap,D=new WeakMap,_t=new WeakMap,yt=new WeakMap,H=new WeakSet,$=function(t){const s=r=>{switch(t.type){case"failed":return{...r,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...r,fetchStatus:"paused"};case"continue":return{...r,fetchStatus:"fetching"};case"fetch":return{...r,...ze(r.data,this.options),fetchMeta:t.meta??null};case"success":return{...r,data:t.data,dataUpdateCount:r.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const n=t.error;return Xt(n)&&n.revert&&i(this,Ft)?{...i(this,Ft),fetchStatus:"idle"}:{...r,error:n,errorUpdateCount:r.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:r.fetchFailureCount+1,fetchFailureReason:n,fetchStatus:"idle",status:"error"};case"invalidate":return{...r,isInvalidated:!0};case"setState":return{...r,...t.state}}};this.state=s(this.state),Q.batch(()=>{this.observers.forEach(r=>{r.onQueryUpdate()}),i(this,_).notify({query:this,type:"updated",action:t})})},De);function ze(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:He(t.networkMode)?"fetching":"paused",...e===void 0&&{error:null,status:"pending"}}}function ls(e){const t=typeof e.initialData=="function"?e.initialData():e.initialData,s=t!==void 0,r=s?typeof e.initialDataUpdatedAt=="function"?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:s?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}var N,Te,ds=(Te=class extends It{constructor(t={}){super();c(this,N);this.config=t,u(this,N,new Map)}build(t,s,r){const n=s.queryKey,a=s.queryHash??de(n,s);let o=this.get(a);return o||(o=new cs({client:t,queryKey:n,queryHash:a,options:t.defaultQueryOptions(s),state:r,defaultOptions:t.getQueryDefaults(n)}),this.add(o)),o}add(t){i(this,N).has(t.queryHash)||(i(this,N).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const s=i(this,N).get(t.queryHash);s&&(t.destroy(),s===t&&i(this,N).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){Q.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return i(this,N).get(t)}getAll(){return[...i(this,N).values()]}find(t){const s={exact:!0,...t};return this.getAll().find(r=>ge(s,r))}findAll(t={}){const s=this.getAll();return Object.keys(t).length>0?s.filter(r=>ge(t,r)):s}notify(t){Q.batch(()=>{this.listeners.forEach(s=>{s(t)})})}onFocus(){Q.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){Q.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},N=new WeakMap,Te),z,q,pt,J,X,Ae,fs=(Ae=class extends Ne{constructor(t){super();c(this,J);c(this,z);c(this,q);c(this,pt);this.mutationId=t.mutationId,u(this,q,t.mutationCache),u(this,z,[]),this.state=t.state||Je(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){i(this,z).includes(t)||(i(this,z).push(t),this.clearGcTimeout(),i(this,q).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){u(this,z,i(this,z).filter(s=>s!==t)),this.scheduleGc(),i(this,q).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){i(this,z).length||(this.state.status==="pending"?this.scheduleGc():i(this,q).remove(this))}continue(){var t;return((t=i(this,pt))==null?void 0:t.continue())??this.execute(this.state.variables)}async execute(t){var a,o,h,d,g,R,l,p,m,O,f,y,P,T,w,x,L,K,Ut,A;const s=()=>{v(this,J,X).call(this,{type:"continue"})};u(this,pt,Be({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(F,E)=>{v(this,J,X).call(this,{type:"failed",failureCount:F,error:E})},onPause:()=>{v(this,J,X).call(this,{type:"pause"})},onContinue:s,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>i(this,q).canRun(this)}));const r=this.state.status==="pending",n=!i(this,pt).canStart();try{if(r)s();else{v(this,J,X).call(this,{type:"pending",variables:t,isPaused:n}),await((o=(a=i(this,q).config).onMutate)==null?void 0:o.call(a,t,this));const E=await((d=(h=this.options).onMutate)==null?void 0:d.call(h,t));E!==this.state.context&&v(this,J,X).call(this,{type:"pending",context:E,variables:t,isPaused:n})}const F=await i(this,pt).start();return await((R=(g=i(this,q).config).onSuccess)==null?void 0:R.call(g,F,t,this.state.context,this)),await((p=(l=this.options).onSuccess)==null?void 0:p.call(l,F,t,this.state.context)),await((O=(m=i(this,q).config).onSettled)==null?void 0:O.call(m,F,null,this.state.variables,this.state.context,this)),await((y=(f=this.options).onSettled)==null?void 0:y.call(f,F,null,t,this.state.context)),v(this,J,X).call(this,{type:"success",data:F}),F}catch(F){try{throw await((T=(P=i(this,q).config).onError)==null?void 0:T.call(P,F,t,this.state.context,this)),await((x=(w=this.options).onError)==null?void 0:x.call(w,F,t,this.state.context)),await((K=(L=i(this,q).config).onSettled)==null?void 0:K.call(L,void 0,F,this.state.variables,this.state.context,this)),await((A=(Ut=this.options).onSettled)==null?void 0:A.call(Ut,void 0,F,t,this.state.context)),F}finally{v(this,J,X).call(this,{type:"error",error:F})}}finally{i(this,q).runNext(this)}}},z=new WeakMap,q=new WeakMap,pt=new WeakMap,J=new WeakSet,X=function(t){const s=r=>{switch(t.type){case"failed":return{...r,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...r,isPaused:!0};case"continue":return{...r,isPaused:!1};case"pending":return{...r,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...r,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...r,data:void 0,error:t.error,failureCount:r.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}};this.state=s(this.state),Q.batch(()=>{i(this,z).forEach(r=>{r.onMutationUpdate(t)}),i(this,q).notify({mutation:this,type:"updated",action:t})})},Ae);function Je(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var V,G,Kt,qe,ys=(qe=class extends It{constructor(t={}){super();c(this,V);c(this,G);c(this,Kt);this.config=t,u(this,V,new Set),u(this,G,new Map),u(this,Kt,0)}build(t,s,r){const n=new fs({mutationCache:this,mutationId:++Nt(this,Kt)._,options:t.defaultMutationOptions(s),state:r});return this.add(n),n}add(t){i(this,V).add(t);const s=zt(t);if(typeof s=="string"){const r=i(this,G).get(s);r?r.push(t):i(this,G).set(s,[t])}this.notify({type:"added",mutation:t})}remove(t){if(i(this,V).delete(t)){const s=zt(t);if(typeof s=="string"){const r=i(this,G).get(s);if(r)if(r.length>1){const n=r.indexOf(t);n!==-1&&r.splice(n,1)}else r[0]===t&&i(this,G).delete(s)}}this.notify({type:"removed",mutation:t})}canRun(t){const s=zt(t);if(typeof s=="string"){const r=i(this,G).get(s),n=r==null?void 0:r.find(a=>a.state.status==="pending");return!n||n===t}else return!0}runNext(t){var r;const s=zt(t);if(typeof s=="string"){const n=(r=i(this,G).get(s))==null?void 0:r.find(a=>a!==t&&a.state.isPaused);return(n==null?void 0:n.continue())??Promise.resolve()}else return Promise.resolve()}clear(){Q.batch(()=>{i(this,V).forEach(t=>{this.notify({type:"removed",mutation:t})}),i(this,V).clear(),i(this,G).clear()})}getAll(){return Array.from(i(this,V))}find(t){const s={exact:!0,...t};return this.getAll().find(r=>Ce(s,r))}findAll(t={}){return this.getAll().filter(s=>Ce(t,s))}notify(t){Q.batch(()=>{this.listeners.forEach(s=>{s(t)})})}resumePausedMutations(){const t=this.getAll().filter(s=>s.state.isPaused);return Q.batch(()=>Promise.all(t.map(s=>s.continue().catch(U))))}},V=new WeakMap,G=new WeakMap,Kt=new WeakMap,qe);function zt(e){var t;return(t=e.options.scope)==null?void 0:t.id}function we(e){return{onFetch:(t,s)=>{var R,l,p,m,O;const r=t.options,n=(p=(l=(R=t.fetchOptions)==null?void 0:R.meta)==null?void 0:l.fetchMore)==null?void 0:p.direction,a=((m=t.state.data)==null?void 0:m.pages)||[],o=((O=t.state.data)==null?void 0:O.pageParams)||[];let h={pages:[],pageParams:[]},d=0;const g=async()=>{let f=!1;const y=w=>{Object.defineProperty(w,"signal",{enumerable:!0,get:()=>(t.signal.aborted?f=!0:t.signal.addEventListener("abort",()=>{f=!0}),t.signal)})},P=_e(t.options,t.fetchOptions),T=async(w,x,L)=>{if(f)return Promise.reject();if(x==null&&w.pages.length)return Promise.resolve(w);const Ut=(()=>{const ct={client:t.client,queryKey:t.queryKey,pageParam:x,direction:L?"backward":"forward",meta:t.options.meta};return y(ct),ct})(),A=await P(Ut),{maxPages:F}=t.options,E=L?rs:is;return{pages:E(w.pages,A,F),pageParams:E(w.pageParams,x,F)}};if(n&&a.length){const w=n==="backward",x=w?ps:Se,L={pages:a,pageParams:o},K=x(r,L);h=await T(L,K,w)}else{const w=e??a.length;do{const x=d===0?o[0]??r.initialPageParam:Se(r,h);if(d>0&&x==null)break;h=await T(h,x),d++}while(d<w)}return h};t.options.persister?t.fetchFn=()=>{var f,y;return(y=(f=t.options).persister)==null?void 0:y.call(f,g,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},s)}:t.fetchFn=g}}}function Se(e,{pages:t,pageParams:s}){const r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,s[r],s):void 0}function ps(e,{pages:t,pageParams:s}){var r;return t.length>0?(r=e.getPreviousPageParam)==null?void 0:r.call(e,t[0],t,s[0],s):void 0}var S,et,st,Et,Qt,it,xt,Mt,Ie,As=(Ie=class{constructor(e={}){c(this,S);c(this,et);c(this,st);c(this,Et);c(this,Qt);c(this,it);c(this,xt);c(this,Mt);u(this,S,e.queryCache||new ds),u(this,et,e.mutationCache||new ys),u(this,st,e.defaultOptions||{}),u(this,Et,new Map),u(this,Qt,new Map),u(this,it,0)}mount(){Nt(this,it)._++,i(this,it)===1&&(u(this,xt,ye.subscribe(async e=>{e&&(await this.resumePausedMutations(),i(this,S).onFocus())})),u(this,Mt,Vt.subscribe(async e=>{e&&(await this.resumePausedMutations(),i(this,S).onOnline())})))}unmount(){var e,t;Nt(this,it)._--,i(this,it)===0&&((e=i(this,xt))==null||e.call(this),u(this,xt,void 0),(t=i(this,Mt))==null||t.call(this),u(this,Mt,void 0))}isFetching(e){return i(this,S).findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return i(this,et).findAll({...e,status:"pending"}).length}getQueryData(e){var s;const t=this.defaultQueryOptions({queryKey:e});return(s=i(this,S).get(t.queryHash))==null?void 0:s.state.data}ensureQueryData(e){const t=this.defaultQueryOptions(e),s=i(this,S).build(this,t),r=s.state.data;return r===void 0?this.fetchQuery(e):(e.revalidateIfStale&&s.isStaleByTime(ht(t.staleTime,s))&&this.prefetchQuery(t),Promise.resolve(r))}getQueriesData(e){return i(this,S).findAll(e).map(({queryKey:t,state:s})=>{const r=s.data;return[t,r]})}setQueryData(e,t,s){const r=this.defaultQueryOptions({queryKey:e}),n=i(this,S).get(r.queryHash),a=n==null?void 0:n.state.data,o=es(t,a);if(o!==void 0)return i(this,S).build(this,r).setData(o,{...s,manual:!0})}setQueriesData(e,t,s){return Q.batch(()=>i(this,S).findAll(e).map(({queryKey:r})=>[r,this.setQueryData(r,t,s)]))}getQueryState(e){var s;const t=this.defaultQueryOptions({queryKey:e});return(s=i(this,S).get(t.queryHash))==null?void 0:s.state}removeQueries(e){const t=i(this,S);Q.batch(()=>{t.findAll(e).forEach(s=>{t.remove(s)})})}resetQueries(e,t){const s=i(this,S);return Q.batch(()=>(s.findAll(e).forEach(r=>{r.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){const s={revert:!0,...t},r=Q.batch(()=>i(this,S).findAll(e).map(n=>n.cancel(s)));return Promise.all(r).then(U).catch(U)}invalidateQueries(e,t={}){return Q.batch(()=>(i(this,S).findAll(e).forEach(s=>{s.invalidate()}),(e==null?void 0:e.refetchType)==="none"?Promise.resolve():this.refetchQueries({...e,type:(e==null?void 0:e.refetchType)??(e==null?void 0:e.type)??"active"},t)))}refetchQueries(e,t={}){const s={...t,cancelRefetch:t.cancelRefetch??!0},r=Q.batch(()=>i(this,S).findAll(e).filter(n=>!n.isDisabled()&&!n.isStatic()).map(n=>{let a=n.fetch(void 0,s);return s.throwOnError||(a=a.catch(U)),n.state.fetchStatus==="paused"?Promise.resolve():a}));return Promise.all(r).then(U)}fetchQuery(e){const t=this.defaultQueryOptions(e);t.retry===void 0&&(t.retry=!1);const s=i(this,S).build(this,t);return s.isStaleByTime(ht(t.staleTime,s))?s.fetch(t):Promise.resolve(s.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(U).catch(U)}fetchInfiniteQuery(e){return e.behavior=we(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(U).catch(U)}ensureInfiniteQueryData(e){return e.behavior=we(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return Vt.isOnline()?i(this,et).resumePausedMutations():Promise.resolve()}getQueryCache(){return i(this,S)}getMutationCache(){return i(this,et)}getDefaultOptions(){return i(this,st)}setDefaultOptions(e){u(this,st,e)}setQueryDefaults(e,t){i(this,Et).set(Ct(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...i(this,Et).values()],s={};return t.forEach(r=>{Lt(e,r.queryKey)&&Object.assign(s,r.defaultOptions)}),s}setMutationDefaults(e,t){i(this,Qt).set(Ct(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...i(this,Qt).values()],s={};return t.forEach(r=>{Lt(e,r.mutationKey)&&Object.assign(s,r.defaultOptions)}),s}defaultQueryOptions(e){if(e._defaulted)return e;const t={...i(this,st).queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=de(t.queryKey,t)),t.refetchOnReconnect===void 0&&(t.refetchOnReconnect=t.networkMode!=="always"),t.throwOnError===void 0&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===fe&&(t.enabled=!1),t}defaultMutationOptions(e){return e!=null&&e._defaulted?e:{...i(this,st).mutations,...(e==null?void 0:e.mutationKey)&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){i(this,S).clear(),i(this,et).clear()}},S=new WeakMap,et=new WeakMap,st=new WeakMap,Et=new WeakMap,Qt=new WeakMap,it=new WeakMap,xt=new WeakMap,Mt=new WeakMap,Ie),j,b,Ht,I,vt,Dt,rt,nt,Gt,Tt,At,mt,bt,at,qt,C,kt,ie,re,ne,ae,ue,oe,he,$e,Ue,vs=(Ue=class extends It{constructor(t,s){super();c(this,C);c(this,j);c(this,b);c(this,Ht);c(this,I);c(this,vt);c(this,Dt);c(this,rt);c(this,nt);c(this,Gt);c(this,Tt);c(this,At);c(this,mt);c(this,bt);c(this,at);c(this,qt,new Set);this.options=s,u(this,j,t),u(this,nt,null),u(this,rt,se()),this.options.experimental_prefetchInRender||i(this,rt).reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(s)}bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(i(this,b).addObserver(this),Pe(i(this,b),this.options)?v(this,C,kt).call(this):this.updateResult(),v(this,C,ae).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return ce(i(this,b),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return ce(i(this,b),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,v(this,C,ue).call(this),v(this,C,oe).call(this),i(this,b).removeObserver(this)}setOptions(t){const s=this.options,r=i(this,b);if(this.options=i(this,j).defaultQueryOptions(t),this.options.enabled!==void 0&&typeof this.options.enabled!="boolean"&&typeof this.options.enabled!="function"&&typeof B(this.options.enabled,i(this,b))!="boolean")throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");v(this,C,he).call(this),i(this,b).setOptions(this.options),s._defaulted&&!$t(this.options,s)&&i(this,j).getQueryCache().notify({type:"observerOptionsUpdated",query:i(this,b),observer:this});const n=this.hasListeners();n&&Fe(i(this,b),r,this.options,s)&&v(this,C,kt).call(this),this.updateResult(),n&&(i(this,b)!==r||B(this.options.enabled,i(this,b))!==B(s.enabled,i(this,b))||ht(this.options.staleTime,i(this,b))!==ht(s.staleTime,i(this,b)))&&v(this,C,ie).call(this);const a=v(this,C,re).call(this);n&&(i(this,b)!==r||B(this.options.enabled,i(this,b))!==B(s.enabled,i(this,b))||a!==i(this,at))&&v(this,C,ne).call(this,a)}getOptimisticResult(t){const s=i(this,j).getQueryCache().build(i(this,j),t),r=this.createResult(s,t);return bs(this,r)&&(u(this,I,r),u(this,Dt,this.options),u(this,vt,i(this,b).state)),r}getCurrentResult(){return i(this,I)}trackResult(t,s){return new Proxy(t,{get:(r,n)=>(this.trackProp(n),s==null||s(n),Reflect.get(r,n))})}trackProp(t){i(this,qt).add(t)}getCurrentQuery(){return i(this,b)}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){const s=i(this,j).defaultQueryOptions(t),r=i(this,j).getQueryCache().build(i(this,j),s);return r.fetch().then(()=>this.createResult(r,s))}fetch(t){return v(this,C,kt).call(this,{...t,cancelRefetch:t.cancelRefetch??!0}).then(()=>(this.updateResult(),i(this,I)))}createResult(t,s){var F;const r=i(this,b),n=this.options,a=i(this,I),o=i(this,vt),h=i(this,Dt),g=t!==r?t.state:i(this,Ht),{state:R}=t;let l={...R},p=!1,m;if(s._optimisticResults){const E=this.hasListeners(),ct=!E&&Pe(t,s),Rt=E&&Fe(t,r,s,n);(ct||Rt)&&(l={...l,...ze(R.data,t.options)}),s._optimisticResults==="isRestoring"&&(l.fetchStatus="idle")}let{error:O,errorUpdatedAt:f,status:y}=l;m=l.data;let P=!1;if(s.placeholderData!==void 0&&m===void 0&&y==="pending"){let E;a!=null&&a.isPlaceholderData&&s.placeholderData===(h==null?void 0:h.placeholderData)?(E=a.data,P=!0):E=typeof s.placeholderData=="function"?s.placeholderData((F=i(this,At))==null?void 0:F.state.data,i(this,At)):s.placeholderData,E!==void 0&&(y="success",m=ee(a==null?void 0:a.data,E,s),p=!0)}if(s.select&&m!==void 0&&!P)if(a&&m===(o==null?void 0:o.data)&&s.select===i(this,Gt))m=i(this,Tt);else try{u(this,Gt,s.select),m=s.select(m),m=ee(a==null?void 0:a.data,m,s),u(this,Tt,m),u(this,nt,null)}catch(E){u(this,nt,E)}i(this,nt)&&(O=i(this,nt),m=i(this,Tt),f=Date.now(),y="error");const T=l.fetchStatus==="fetching",w=y==="pending",x=y==="error",L=w&&T,K=m!==void 0,A={status:y,fetchStatus:l.fetchStatus,isPending:w,isSuccess:y==="success",isError:x,isInitialLoading:L,isLoading:L,data:m,dataUpdatedAt:l.dataUpdatedAt,error:O,errorUpdatedAt:f,failureCount:l.fetchFailureCount,failureReason:l.fetchFailureReason,errorUpdateCount:l.errorUpdateCount,isFetched:l.dataUpdateCount>0||l.errorUpdateCount>0,isFetchedAfterMount:l.dataUpdateCount>g.dataUpdateCount||l.errorUpdateCount>g.errorUpdateCount,isFetching:T,isRefetching:T&&!w,isLoadingError:x&&!K,isPaused:l.fetchStatus==="paused",isPlaceholderData:p,isRefetchError:x&&K,isStale:pe(t,s),refetch:this.refetch,promise:i(this,rt)};if(this.options.experimental_prefetchInRender){const E=Bt=>{A.status==="error"?Bt.reject(A.error):A.data!==void 0&&Bt.resolve(A.data)},ct=()=>{const Bt=u(this,rt,A.promise=se());E(Bt)},Rt=i(this,rt);switch(Rt.status){case"pending":t.queryHash===r.queryHash&&E(Rt);break;case"fulfilled":(A.status==="error"||A.data!==Rt.value)&&ct();break;case"rejected":(A.status!=="error"||A.error!==Rt.reason)&&ct();break}}return A}updateResult(){const t=i(this,I),s=this.createResult(i(this,b),this.options);if(u(this,vt,i(this,b).state),u(this,Dt,this.options),i(this,vt).data!==void 0&&u(this,At,i(this,b)),$t(s,t))return;u(this,I,s);const r=()=>{if(!t)return!0;const{notifyOnChangeProps:n}=this.options,a=typeof n=="function"?n():n;if(a==="all"||!a&&!i(this,qt).size)return!0;const o=new Set(a??i(this,qt));return this.options.throwOnError&&o.add("error"),Object.keys(i(this,I)).some(h=>{const d=h;return i(this,I)[d]!==t[d]&&o.has(d)})};v(this,C,$e).call(this,{listeners:r()})}onQueryUpdate(){this.updateResult(),this.hasListeners()&&v(this,C,ae).call(this)}},j=new WeakMap,b=new WeakMap,Ht=new WeakMap,I=new WeakMap,vt=new WeakMap,Dt=new WeakMap,rt=new WeakMap,nt=new WeakMap,Gt=new WeakMap,Tt=new WeakMap,At=new WeakMap,mt=new WeakMap,bt=new WeakMap,at=new WeakMap,qt=new WeakMap,C=new WeakSet,kt=function(t){v(this,C,he).call(this);let s=i(this,b).fetch(this.options,t);return t!=null&&t.throwOnError||(s=s.catch(U)),s},ie=function(){v(this,C,ue).call(this);const t=ht(this.options.staleTime,i(this,b));if(gt||i(this,I).isStale||!Zt(t))return;const r=ke(i(this,I).dataUpdatedAt,t)+1;u(this,mt,setTimeout(()=>{i(this,I).isStale||this.updateResult()},r))},re=function(){return(typeof this.options.refetchInterval=="function"?this.options.refetchInterval(i(this,b)):this.options.refetchInterval)??!1},ne=function(t){v(this,C,oe).call(this),u(this,at,t),!(gt||B(this.options.enabled,i(this,b))===!1||!Zt(i(this,at))||i(this,at)===0)&&u(this,bt,setInterval(()=>{(this.options.refetchIntervalInBackground||ye.isFocused())&&v(this,C,kt).call(this)},i(this,at)))},ae=function(){v(this,C,ie).call(this),v(this,C,ne).call(this,v(this,C,re).call(this))},ue=function(){i(this,mt)&&(clearTimeout(i(this,mt)),u(this,mt,void 0))},oe=function(){i(this,bt)&&(clearInterval(i(this,bt)),u(this,bt,void 0))},he=function(){const t=i(this,j).getQueryCache().build(i(this,j),this.options);if(t===i(this,b))return;const s=i(this,b);u(this,b,t),u(this,Ht,t.state),this.hasListeners()&&(s==null||s.removeObserver(this),t.addObserver(this))},$e=function(t){Q.batch(()=>{t.listeners&&this.listeners.forEach(s=>{s(i(this,I))}),i(this,j).getQueryCache().notify({query:i(this,b),type:"observerResultsUpdated"})})},Ue);function ms(e,t){return B(t.enabled,e)!==!1&&e.state.data===void 0&&!(e.state.status==="error"&&t.retryOnMount===!1)}function Pe(e,t){return ms(e,t)||e.state.data!==void 0&&ce(e,t,t.refetchOnMount)}function ce(e,t,s){if(B(t.enabled,e)!==!1&&ht(t.staleTime,e)!=="static"){const r=typeof s=="function"?s(e):s;return r==="always"||r!==!1&&pe(e,t)}return!1}function Fe(e,t,s,r){return(e!==t||B(r.enabled,e)===!1)&&(!s.suspense||e.state.status!=="error")&&pe(e,s)}function pe(e,t){return B(t.enabled,e)!==!1&&e.isStaleByTime(ht(t.staleTime,e))}function bs(e,t){return!$t(e.getCurrentResult(),t)}var ut,ot,k,W,Y,Jt,le,je,gs=(je=class extends It{constructor(t,s){super();c(this,Y);c(this,ut);c(this,ot);c(this,k);c(this,W);u(this,ut,t),this.setOptions(s),this.bindMethods(),v(this,Y,Jt).call(this)}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(t){var r;const s=this.options;this.options=i(this,ut).defaultMutationOptions(t),$t(this.options,s)||i(this,ut).getMutationCache().notify({type:"observerOptionsUpdated",mutation:i(this,k),observer:this}),s!=null&&s.mutationKey&&this.options.mutationKey&&Ct(s.mutationKey)!==Ct(this.options.mutationKey)?this.reset():((r=i(this,k))==null?void 0:r.state.status)==="pending"&&i(this,k).setOptions(this.options)}onUnsubscribe(){var t;this.hasListeners()||(t=i(this,k))==null||t.removeObserver(this)}onMutationUpdate(t){v(this,Y,Jt).call(this),v(this,Y,le).call(this,t)}getCurrentResult(){return i(this,ot)}reset(){var t;(t=i(this,k))==null||t.removeObserver(this),u(this,k,void 0),v(this,Y,Jt).call(this),v(this,Y,le).call(this)}mutate(t,s){var r;return u(this,W,s),(r=i(this,k))==null||r.removeObserver(this),u(this,k,i(this,ut).getMutationCache().build(i(this,ut),this.options)),i(this,k).addObserver(this),i(this,k).execute(t)}},ut=new WeakMap,ot=new WeakMap,k=new WeakMap,W=new WeakMap,Y=new WeakSet,Jt=function(){var s;const t=((s=i(this,k))==null?void 0:s.state)??Je();u(this,ot,{...t,isPending:t.status==="pending",isSuccess:t.status==="success",isError:t.status==="error",isIdle:t.status==="idle",mutate:this.mutate,reset:this.reset})},le=function(t){Q.batch(()=>{var s,r,n,a,o,h,d,g;if(i(this,W)&&this.hasListeners()){const R=i(this,ot).variables,l=i(this,ot).context;(t==null?void 0:t.type)==="success"?((r=(s=i(this,W)).onSuccess)==null||r.call(s,t.data,R,l),(a=(n=i(this,W)).onSettled)==null||a.call(n,t.data,null,R,l)):(t==null?void 0:t.type)==="error"&&((h=(o=i(this,W)).onError)==null||h.call(o,t.error,R,l),(g=(d=i(this,W)).onSettled)==null||g.call(d,void 0,t.error,R,l))}this.listeners.forEach(R=>{R(i(this,ot))})})},je),Ve=M.createContext(void 0),We=e=>{const t=M.useContext(Ve);if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},qs=({client:e,children:t})=>(M.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),ts.jsx(Ve.Provider,{value:e,children:t})),Ye=M.createContext(!1),Cs=()=>M.useContext(Ye);Ye.Provider;function Rs(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}var Os=M.createContext(Rs()),ws=()=>M.useContext(Os),Ss=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&(t.isReset()||(e.retryOnMount=!1))},Ps=e=>{M.useEffect(()=>{e.clearReset()},[e])},Fs=({result:e,errorResetBoundary:t,throwOnError:s,query:r,suspense:n})=>e.isError&&!t.isReset()&&!e.isFetching&&r&&(n&&e.data===void 0||Ke(s,[e.error,r])),Es=e=>{if(e.suspense){const t=r=>r==="static"?r:Math.max(r??1e3,1e3),s=e.staleTime;e.staleTime=typeof s=="function"?(...r)=>t(s(...r)):t(s),typeof e.gcTime=="number"&&(e.gcTime=Math.max(e.gcTime,1e3))}},Qs=(e,t)=>e.isLoading&&e.isFetching&&!t,xs=(e,t)=>(e==null?void 0:e.suspense)&&t.isPending,Ee=(e,t,s)=>t.fetchOptimistic(e).catch(()=>{s.clearReset()});function Ms(e,t,s){var l,p,m,O,f;const r=Cs(),n=ws(),a=We(),o=a.defaultQueryOptions(e);(p=(l=a.getDefaultOptions().queries)==null?void 0:l._experimental_beforeQuery)==null||p.call(l,o),o._optimisticResults=r?"isRestoring":"optimistic",Es(o),Ss(o,n),Ps(n);const h=!a.getQueryCache().get(o.queryHash),[d]=M.useState(()=>new t(a,o)),g=d.getOptimisticResult(o),R=!r&&e.subscribed!==!1;if(M.useSyncExternalStore(M.useCallback(y=>{const P=R?d.subscribe(Q.batchCalls(y)):U;return d.updateResult(),P},[d,R]),()=>d.getCurrentResult(),()=>d.getCurrentResult()),M.useEffect(()=>{d.setOptions(o)},[o,d]),xs(o,g))throw Ee(o,d,n);if(Fs({result:g,errorResetBoundary:n,throwOnError:o.throwOnError,query:a.getQueryCache().get(o.queryHash),suspense:o.suspense}))throw g.error;if((O=(m=a.getDefaultOptions().queries)==null?void 0:m._experimental_afterQuery)==null||O.call(m,o,g),o.experimental_prefetchInRender&&!gt&&Qs(g,r)){const y=h?Ee(o,d,n):(f=a.getQueryCache().get(o.queryHash))==null?void 0:f.promise;y==null||y.catch(U).finally(()=>{d.updateResult()})}return o.notifyOnChangeProps?g:d.trackResult(g)}function Is(e,t){return Ms(e,vs)}function Us(e,t){const s=We(),[r]=M.useState(()=>new gs(s,e));M.useEffect(()=>{r.setOptions(e)},[r,e]);const n=M.useSyncExternalStore(M.useCallback(o=>r.subscribe(Q.batchCalls(o)),[r]),()=>r.getCurrentResult(),()=>r.getCurrentResult()),a=M.useCallback((o,h)=>{r.mutate(o,h).catch(U)},[r]);if(n.error&&Ke(r.options.throwOnError,[n.error]))throw n.error;return{...n,mutate:a,mutateAsync:n.mutate}}export{qs as Q,Is as a,As as b,We as c,ts as j,Us as u};
