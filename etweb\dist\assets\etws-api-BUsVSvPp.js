var E=Object.defineProperty;var R=(r,a,s)=>a in r?E(r,a,{enumerable:!0,configurable:!0,writable:!0,value:s}):r[a]=s;var q=(r,a,s)=>R(r,typeof a!="symbol"?a+"":a,s);import{a as y}from"./api-client-xsH4HHeE.js";const I="http://microit9app1.drcenter.btk.bg:26121/et-ws".replace(/\/+$/,"");class b{constructor(a,s=I,e=y){q(this,"configuration");this.basePath=s,this.axios=e,a&&(this.configuration=a,this.basePath=a.basePath??s)}}class w extends Error{constructor(a,s){super(s),this.field=a,this.name="RequiredError"}}const P={},S="https://example.com",u=function(r,a,s){if(s==null)throw new w(a,`Required parameter ${a} was null or undefined when calling ${r}.`)};function v(r,a,s=""){a!=null&&(typeof a=="object"?Array.isArray(a)?a.forEach(e=>v(r,e,s)):Object.keys(a).forEach(e=>v(r,a[e],`${s}${s!==""?".":""}${e}`)):r.has(s)?r.append(s,a):r.set(s,a))}const O=function(r,...a){const s=new URLSearchParams(r.search);v(s,a),r.search=s.toString()},A=function(r,a,s){const e=typeof r!="string";return(e&&s&&s.isJsonMime?s.isJsonMime(a.headers["Content-Type"]):e)?JSON.stringify(r!==void 0?r:{}):r||""},V=function(r){return r.pathname+r.search+r.hash},U=function(r,a,s,e){return(l=a,c=s)=>{const i={...r.options,url:(l.defaults.baseURL?"":(e==null?void 0:e.basePath)??c)+r.url};return l.request(i)}},L=function(r){return{apiAccountDeleteImpersonationCookieGet:async(a,s,e={})=>{const l="/api/account/delete-impersonation-cookie",c=new URL(l,S);let i;r&&(i=r.baseOptions);const t={method:"GET",...i,...e},o={},n={};a!=null&&(o["Input-Request-Id"]=String(a)),s!=null&&(o["Input-Timestamp"]=typeof s=="string"?s:JSON.stringify(s)),O(c,n);let p=i&&i.headers?i.headers:{};return t.headers={...o,...p,...e.headers},{url:V(c),options:t}},apiAccountLoginPost:async(a,s,e,l={})=>{u("apiAccountLoginPost","userLoginRequestModel",a);const c="/api/account/login",i=new URL(c,S);let t;r&&(t=r.baseOptions);const o={method:"POST",...t,...l},n={},p={};n["Content-Type"]="application/json",s!=null&&(n["Input-Request-Id"]=String(s)),e!=null&&(n["Input-Timestamp"]=typeof e=="string"?e:JSON.stringify(e)),O(i,p);let h=t&&t.headers?t.headers:{};return o.headers={...n,...h,...l.headers},o.data=A(a,o,r),{url:V(i),options:o}},apiAccountLogoutPost:async(a,s,e={})=>{const l="/api/account/logout",c=new URL(l,S);let i;r&&(i=r.baseOptions);const t={method:"POST",...i,...e},o={},n={};a!=null&&(o["Input-Request-Id"]=String(a)),s!=null&&(o["Input-Timestamp"]=typeof s=="string"?s:JSON.stringify(s)),O(c,n);let p=i&&i.headers?i.headers:{};return t.headers={...o,...p,...e.headers},{url:V(c),options:t}},apiAccountSetImpersonationCookieGet:async(a,s,e={})=>{const l="/api/account/set-impersonation-cookie",c=new URL(l,S);let i;r&&(i=r.baseOptions);const t={method:"GET",...i,...e},o={},n={};a!=null&&(o["Input-Request-Id"]=String(a)),s!=null&&(o["Input-Timestamp"]=typeof s=="string"?s:JSON.stringify(s)),O(c,n);let p=i&&i.headers?i.headers:{};return t.headers={...o,...p,...e.headers},{url:V(c),options:t}}}},G=function(r){const a=L(r);return{async apiAccountDeleteImpersonationCookieGet(s,e,l){var o,n;const c=await a.apiAccountDeleteImpersonationCookieGet(s,e,l),i=(r==null?void 0:r.serverIndex)??0,t=(n=(o=P["AccountApi.apiAccountDeleteImpersonationCookieGet"])==null?void 0:o[i])==null?void 0:n.url;return(p,h)=>U(c,y,I,r)(p,t||h)},async apiAccountLoginPost(s,e,l,c){var n,p;const i=await a.apiAccountLoginPost(s,e,l,c),t=(r==null?void 0:r.serverIndex)??0,o=(p=(n=P["AccountApi.apiAccountLoginPost"])==null?void 0:n[t])==null?void 0:p.url;return(h,d)=>U(i,y,I,r)(h,o||d)},async apiAccountLogoutPost(s,e,l){var o,n;const c=await a.apiAccountLogoutPost(s,e,l),i=(r==null?void 0:r.serverIndex)??0,t=(n=(o=P["AccountApi.apiAccountLogoutPost"])==null?void 0:o[i])==null?void 0:n.url;return(p,h)=>U(c,y,I,r)(p,t||h)},async apiAccountSetImpersonationCookieGet(s,e,l){var o,n;const c=await a.apiAccountSetImpersonationCookieGet(s,e,l),i=(r==null?void 0:r.serverIndex)??0,t=(n=(o=P["AccountApi.apiAccountSetImpersonationCookieGet"])==null?void 0:o[i])==null?void 0:n.url;return(p,h)=>U(c,y,I,r)(p,t||h)}}};class D extends b{apiAccountDeleteImpersonationCookieGet(a,s,e){return G(this.configuration).apiAccountDeleteImpersonationCookieGet(a,s,e).then(l=>l(this.axios,this.basePath))}apiAccountLoginPost(a,s,e,l){return G(this.configuration).apiAccountLoginPost(a,s,e,l).then(c=>c(this.axios,this.basePath))}apiAccountLogoutPost(a,s,e){return G(this.configuration).apiAccountLogoutPost(a,s,e).then(l=>l(this.axios,this.basePath))}apiAccountSetImpersonationCookieGet(a,s,e){return G(this.configuration).apiAccountSetImpersonationCookieGet(a,s,e).then(l=>l(this.axios,this.basePath))}}const N=function(r){return{apiCitiesActivatePut:async(a,s,e,l={})=>{const c="/api/cities/activate",i=new URL(c,S);let t;r&&(t=r.baseOptions);const o={method:"PUT",...t,...l},n={},p={};n["Content-Type"]="application/json",a!=null&&(n["Input-Request-Id"]=String(a)),s!=null&&(n["Input-Timestamp"]=typeof s=="string"?s:JSON.stringify(s)),O(i,p);let h=t&&t.headers?t.headers:{};return o.headers={...n,...h,...l.headers},o.data=A(e,o,r),{url:V(i),options:o}},apiCitiesAddPost:async(a,s,e,l={})=>{u("apiCitiesAddPost","cityRequestModel",a);const c="/api/cities/add",i=new URL(c,S);let t;r&&(t=r.baseOptions);const o={method:"POST",...t,...l},n={},p={};n["Content-Type"]="application/json",s!=null&&(n["Input-Request-Id"]=String(s)),e!=null&&(n["Input-Timestamp"]=typeof e=="string"?e:JSON.stringify(e)),O(i,p);let h=t&&t.headers?t.headers:{};return o.headers={...n,...h,...l.headers},o.data=A(a,o,r),{url:V(i),options:o}},apiCitiesBlockPut:async(a,s,e,l={})=>{const c="/api/cities/block",i=new URL(c,S);let t;r&&(t=r.baseOptions);const o={method:"PUT",...t,...l},n={},p={};n["Content-Type"]="application/json",a!=null&&(n["Input-Request-Id"]=String(a)),s!=null&&(n["Input-Timestamp"]=typeof s=="string"?s:JSON.stringify(s)),O(i,p);let h=t&&t.headers?t.headers:{};return o.headers={...n,...h,...l.headers},o.data=A(e,o,r),{url:V(i),options:o}},apiCitiesByIdGet:async(a,s,e,l={})=>{u("apiCitiesByIdGet","id",a);const c="/api/cities/{id}".replace("{id}",encodeURIComponent(String(a))),i=new URL(c,S);let t;r&&(t=r.baseOptions);const o={method:"GET",...t,...l},n={},p={};s!=null&&(n["Input-Request-Id"]=String(s)),e!=null&&(n["Input-Timestamp"]=typeof e=="string"?e:JSON.stringify(e)),O(i,p);let h=t&&t.headers?t.headers:{};return o.headers={...n,...h,...l.headers},{url:V(i),options:o}},apiCitiesRegionByIdGet:async(a,s,e,l={})=>{u("apiCitiesRegionByIdGet","id",a);const c="/api/cities/region/{id}".replace("{id}",encodeURIComponent(String(a))),i=new URL(c,S);let t;r&&(t=r.baseOptions);const o={method:"GET",...t,...l},n={},p={};s!=null&&(n["Input-Request-Id"]=String(s)),e!=null&&(n["Input-Timestamp"]=typeof e=="string"?e:JSON.stringify(e)),O(i,p);let h=t&&t.headers?t.headers:{};return o.headers={...n,...h,...l.headers},{url:V(i),options:o}},apiCitiesSapCityCodeAndClusterByCityIdGet:async(a,s,e,l={})=>{u("apiCitiesSapCityCodeAndClusterByCityIdGet","cityId",a);const c="/api/cities/sap-city-code-and-cluster/{cityId}".replace("{cityId}",encodeURIComponent(String(a))),i=new URL(c,S);let t;r&&(t=r.baseOptions);const o={method:"GET",...t,...l},n={},p={};s!=null&&(n["Input-Request-Id"]=String(s)),e!=null&&(n["Input-Timestamp"]=typeof e=="string"?e:JSON.stringify(e)),O(i,p);let h=t&&t.headers?t.headers:{};return o.headers={...n,...h,...l.headers},{url:V(i),options:o}},apiCitiesSearchPost:async(a,s,e,l={})=>{u("apiCitiesSearchPost","searchDataRequestModel",a);const c="/api/cities/search",i=new URL(c,S);let t;r&&(t=r.baseOptions);const o={method:"POST",...t,...l},n={},p={};n["Content-Type"]="application/json",s!=null&&(n["Input-Request-Id"]=String(s)),e!=null&&(n["Input-Timestamp"]=typeof e=="string"?e:JSON.stringify(e)),O(i,p);let h=t&&t.headers?t.headers:{};return o.headers={...n,...h,...l.headers},o.data=A(a,o,r),{url:V(i),options:o}},apiCitiesSelectListGet:async(a,s,e={})=>{const l="/api/cities/select-list",c=new URL(l,S);let i;r&&(i=r.baseOptions);const t={method:"GET",...i,...e},o={},n={};a!=null&&(o["Input-Request-Id"]=String(a)),s!=null&&(o["Input-Timestamp"]=typeof s=="string"?s:JSON.stringify(s)),O(c,n);let p=i&&i.headers?i.headers:{};return t.headers={...o,...p,...e.headers},{url:V(c),options:t}},apiCitiesUpdateByIdPut:async(a,s,e,l,c={})=>{u("apiCitiesUpdateByIdPut","id",a),u("apiCitiesUpdateByIdPut","cityRequestModel",s);const i="/api/cities/update/{id}".replace("{id}",encodeURIComponent(String(a))),t=new URL(i,S);let o;r&&(o=r.baseOptions);const n={method:"PUT",...o,...c},p={},h={};p["Content-Type"]="application/json",e!=null&&(p["Input-Request-Id"]=String(e)),l!=null&&(p["Input-Timestamp"]=typeof l=="string"?l:JSON.stringify(l)),O(t,h);let d=o&&o.headers?o.headers:{};return n.headers={...p,...d,...c.headers},n.data=A(s,n,r),{url:V(t),options:n}}}},m=function(r){const a=N(r);return{async apiCitiesActivatePut(s,e,l,c){var n,p;const i=await a.apiCitiesActivatePut(s,e,l,c),t=(r==null?void 0:r.serverIndex)??0,o=(p=(n=P["CitiesApi.apiCitiesActivatePut"])==null?void 0:n[t])==null?void 0:p.url;return(h,d)=>U(i,y,I,r)(h,o||d)},async apiCitiesAddPost(s,e,l,c){var n,p;const i=await a.apiCitiesAddPost(s,e,l,c),t=(r==null?void 0:r.serverIndex)??0,o=(p=(n=P["CitiesApi.apiCitiesAddPost"])==null?void 0:n[t])==null?void 0:p.url;return(h,d)=>U(i,y,I,r)(h,o||d)},async apiCitiesBlockPut(s,e,l,c){var n,p;const i=await a.apiCitiesBlockPut(s,e,l,c),t=(r==null?void 0:r.serverIndex)??0,o=(p=(n=P["CitiesApi.apiCitiesBlockPut"])==null?void 0:n[t])==null?void 0:p.url;return(h,d)=>U(i,y,I,r)(h,o||d)},async apiCitiesByIdGet(s,e,l,c){var n,p;const i=await a.apiCitiesByIdGet(s,e,l,c),t=(r==null?void 0:r.serverIndex)??0,o=(p=(n=P["CitiesApi.apiCitiesByIdGet"])==null?void 0:n[t])==null?void 0:p.url;return(h,d)=>U(i,y,I,r)(h,o||d)},async apiCitiesRegionByIdGet(s,e,l,c){var n,p;const i=await a.apiCitiesRegionByIdGet(s,e,l,c),t=(r==null?void 0:r.serverIndex)??0,o=(p=(n=P["CitiesApi.apiCitiesRegionByIdGet"])==null?void 0:n[t])==null?void 0:p.url;return(h,d)=>U(i,y,I,r)(h,o||d)},async apiCitiesSapCityCodeAndClusterByCityIdGet(s,e,l,c){var n,p;const i=await a.apiCitiesSapCityCodeAndClusterByCityIdGet(s,e,l,c),t=(r==null?void 0:r.serverIndex)??0,o=(p=(n=P["CitiesApi.apiCitiesSapCityCodeAndClusterByCityIdGet"])==null?void 0:n[t])==null?void 0:p.url;return(h,d)=>U(i,y,I,r)(h,o||d)},async apiCitiesSearchPost(s,e,l,c){var n,p;const i=await a.apiCitiesSearchPost(s,e,l,c),t=(r==null?void 0:r.serverIndex)??0,o=(p=(n=P["CitiesApi.apiCitiesSearchPost"])==null?void 0:n[t])==null?void 0:p.url;return(h,d)=>U(i,y,I,r)(h,o||d)},async apiCitiesSelectListGet(s,e,l){var o,n;const c=await a.apiCitiesSelectListGet(s,e,l),i=(r==null?void 0:r.serverIndex)??0,t=(n=(o=P["CitiesApi.apiCitiesSelectListGet"])==null?void 0:o[i])==null?void 0:n.url;return(p,h)=>U(c,y,I,r)(p,t||h)},async apiCitiesUpdateByIdPut(s,e,l,c,i){var p,h;const t=await a.apiCitiesUpdateByIdPut(s,e,l,c,i),o=(r==null?void 0:r.serverIndex)??0,n=(h=(p=P["CitiesApi.apiCitiesUpdateByIdPut"])==null?void 0:p[o])==null?void 0:h.url;return(d,k)=>U(t,y,I,r)(d,n||k)}}};class $ extends b{apiCitiesActivatePut(a,s,e,l){return m(this.configuration).apiCitiesActivatePut(a,s,e,l).then(c=>c(this.axios,this.basePath))}apiCitiesAddPost(a,s,e,l){return m(this.configuration).apiCitiesAddPost(a,s,e,l).then(c=>c(this.axios,this.basePath))}apiCitiesBlockPut(a,s,e,l){return m(this.configuration).apiCitiesBlockPut(a,s,e,l).then(c=>c(this.axios,this.basePath))}apiCitiesByIdGet(a,s,e,l){return m(this.configuration).apiCitiesByIdGet(a,s,e,l).then(c=>c(this.axios,this.basePath))}apiCitiesRegionByIdGet(a,s,e,l){return m(this.configuration).apiCitiesRegionByIdGet(a,s,e,l).then(c=>c(this.axios,this.basePath))}apiCitiesSapCityCodeAndClusterByCityIdGet(a,s,e,l){return m(this.configuration).apiCitiesSapCityCodeAndClusterByCityIdGet(a,s,e,l).then(c=>c(this.axios,this.basePath))}apiCitiesSearchPost(a,s,e,l){return m(this.configuration).apiCitiesSearchPost(a,s,e,l).then(c=>c(this.axios,this.basePath))}apiCitiesSelectListGet(a,s,e){return m(this.configuration).apiCitiesSelectListGet(a,s,e).then(l=>l(this.axios,this.basePath))}apiCitiesUpdateByIdPut(a,s,e,l,c){return m(this.configuration).apiCitiesUpdateByIdPut(a,s,e,l,c).then(i=>i(this.axios,this.basePath))}}const j=function(r){return{apiIncorrectEquipmentCheckSerialNumberByEquipmentSerialNumGet:async(a,s,e,l={})=>{u("apiIncorrectEquipmentCheckSerialNumberByEquipmentSerialNumGet","equipmentSerialNum",a);const c="/api/incorrect-equipment/check-serial-number/{equipmentSerialNum}".replace("{equipmentSerialNum}",encodeURIComponent(String(a))),i=new URL(c,S);let t;r&&(t=r.baseOptions);const o={method:"GET",...t,...l},n={},p={};s!=null&&(n["Input-Request-Id"]=String(s)),e!=null&&(n["Input-Timestamp"]=typeof e=="string"?e:JSON.stringify(e)),O(i,p);let h=t&&t.headers?t.headers:{};return o.headers={...n,...h,...l.headers},{url:V(i),options:o}},apiIncorrectEquipmentCheckServiceIdByServiceIdGet:async(a,s,e,l={})=>{u("apiIncorrectEquipmentCheckServiceIdByServiceIdGet","serviceId",a);const c="/api/incorrect-equipment/check-service-id/{serviceId}".replace("{serviceId}",encodeURIComponent(String(a))),i=new URL(c,S);let t;r&&(t=r.baseOptions);const o={method:"GET",...t,...l},n={},p={};s!=null&&(n["Input-Request-Id"]=String(s)),e!=null&&(n["Input-Timestamp"]=typeof e=="string"?e:JSON.stringify(e)),O(i,p);let h=t&&t.headers?t.headers:{};return o.headers={...n,...h,...l.headers},{url:V(i),options:o}},apiIncorrectEquipmentGetHistoriesByEquipmentSerialnumberGet:async(a,s,e,l={})=>{u("apiIncorrectEquipmentGetHistoriesByEquipmentSerialnumberGet","equipmentSerialnumber",a);const c="/api/incorrect-equipment/get-histories/{equipmentSerialnumber}".replace("{equipmentSerialnumber}",encodeURIComponent(String(a))),i=new URL(c,S);let t;r&&(t=r.baseOptions);const o={method:"GET",...t,...l},n={},p={};s!=null&&(n["Input-Request-Id"]=String(s)),e!=null&&(n["Input-Timestamp"]=typeof e=="string"?e:JSON.stringify(e)),O(i,p);let h=t&&t.headers?t.headers:{};return o.headers={...n,...h,...l.headers},{url:V(i),options:o}},apiIncorrectEquipmentGetUserDisplayNamesByNamePrefixGet:async(a,s,e,l={})=>{u("apiIncorrectEquipmentGetUserDisplayNamesByNamePrefixGet","namePrefix",a);const c="/api/incorrect-equipment/get-user-display-names/{namePrefix}".replace("{namePrefix}",encodeURIComponent(String(a))),i=new URL(c,S);let t;r&&(t=r.baseOptions);const o={method:"GET",...t,...l},n={},p={};s!=null&&(n["Input-Request-Id"]=String(s)),e!=null&&(n["Input-Timestamp"]=typeof e=="string"?e:JSON.stringify(e)),O(i,p);let h=t&&t.headers?t.headers:{};return o.headers={...n,...h,...l.headers},{url:V(i),options:o}},apiIncorrectEquipmentTransferCorrectionPost:async(a,s,e,l={})=>{const c="/api/incorrect-equipment/transfer-correction",i=new URL(c,S);let t;r&&(t=r.baseOptions);const o={method:"POST",...t,...l},n={},p={};n["Content-Type"]="application/json",a!=null&&(n["Input-Request-Id"]=String(a)),s!=null&&(n["Input-Timestamp"]=typeof s=="string"?s:JSON.stringify(s)),O(i,p);let h=t&&t.headers?t.headers:{};return o.headers={...n,...h,...l.headers},o.data=A(e,o,r),{url:V(i),options:o}},apiIncorrectEquipmentTransferDeleteDelete:async(a,s,e,l={})=>{const c="/api/incorrect-equipment/transfer-delete",i=new URL(c,S);let t;r&&(t=r.baseOptions);const o={method:"DELETE",...t,...l},n={},p={};n["Content-Type"]="application/json",a!=null&&(n["Input-Request-Id"]=String(a)),s!=null&&(n["Input-Timestamp"]=typeof s=="string"?s:JSON.stringify(s)),O(i,p);let h=t&&t.headers?t.headers:{};return o.headers={...n,...h,...l.headers},o.data=A(e,o,r),{url:V(i),options:o}},apiIncorrectEquipmentUsersWithOpcodesGet:async(a,s,e={})=>{const l="/api/incorrect-equipment/users-with-opcodes",c=new URL(l,S);let i;r&&(i=r.baseOptions);const t={method:"GET",...i,...e},o={},n={};a!=null&&(o["Input-Request-Id"]=String(a)),s!=null&&(o["Input-Timestamp"]=typeof s=="string"?s:JSON.stringify(s)),O(c,n);let p=i&&i.headers?i.headers:{};return t.headers={...o,...p,...e.headers},{url:V(c),options:t}}}},C=function(r){const a=j(r);return{async apiIncorrectEquipmentCheckSerialNumberByEquipmentSerialNumGet(s,e,l,c){var n,p;const i=await a.apiIncorrectEquipmentCheckSerialNumberByEquipmentSerialNumGet(s,e,l,c),t=(r==null?void 0:r.serverIndex)??0,o=(p=(n=P["IncorrectEquipmentApi.apiIncorrectEquipmentCheckSerialNumberByEquipmentSerialNumGet"])==null?void 0:n[t])==null?void 0:p.url;return(h,d)=>U(i,y,I,r)(h,o||d)},async apiIncorrectEquipmentCheckServiceIdByServiceIdGet(s,e,l,c){var n,p;const i=await a.apiIncorrectEquipmentCheckServiceIdByServiceIdGet(s,e,l,c),t=(r==null?void 0:r.serverIndex)??0,o=(p=(n=P["IncorrectEquipmentApi.apiIncorrectEquipmentCheckServiceIdByServiceIdGet"])==null?void 0:n[t])==null?void 0:p.url;return(h,d)=>U(i,y,I,r)(h,o||d)},async apiIncorrectEquipmentGetHistoriesByEquipmentSerialnumberGet(s,e,l,c){var n,p;const i=await a.apiIncorrectEquipmentGetHistoriesByEquipmentSerialnumberGet(s,e,l,c),t=(r==null?void 0:r.serverIndex)??0,o=(p=(n=P["IncorrectEquipmentApi.apiIncorrectEquipmentGetHistoriesByEquipmentSerialnumberGet"])==null?void 0:n[t])==null?void 0:p.url;return(h,d)=>U(i,y,I,r)(h,o||d)},async apiIncorrectEquipmentGetUserDisplayNamesByNamePrefixGet(s,e,l,c){var n,p;const i=await a.apiIncorrectEquipmentGetUserDisplayNamesByNamePrefixGet(s,e,l,c),t=(r==null?void 0:r.serverIndex)??0,o=(p=(n=P["IncorrectEquipmentApi.apiIncorrectEquipmentGetUserDisplayNamesByNamePrefixGet"])==null?void 0:n[t])==null?void 0:p.url;return(h,d)=>U(i,y,I,r)(h,o||d)},async apiIncorrectEquipmentTransferCorrectionPost(s,e,l,c){var n,p;const i=await a.apiIncorrectEquipmentTransferCorrectionPost(s,e,l,c),t=(r==null?void 0:r.serverIndex)??0,o=(p=(n=P["IncorrectEquipmentApi.apiIncorrectEquipmentTransferCorrectionPost"])==null?void 0:n[t])==null?void 0:p.url;return(h,d)=>U(i,y,I,r)(h,o||d)},async apiIncorrectEquipmentTransferDeleteDelete(s,e,l,c){var n,p;const i=await a.apiIncorrectEquipmentTransferDeleteDelete(s,e,l,c),t=(r==null?void 0:r.serverIndex)??0,o=(p=(n=P["IncorrectEquipmentApi.apiIncorrectEquipmentTransferDeleteDelete"])==null?void 0:n[t])==null?void 0:p.url;return(h,d)=>U(i,y,I,r)(h,o||d)},async apiIncorrectEquipmentUsersWithOpcodesGet(s,e,l){var o,n;const c=await a.apiIncorrectEquipmentUsersWithOpcodesGet(s,e,l),i=(r==null?void 0:r.serverIndex)??0,t=(n=(o=P["IncorrectEquipmentApi.apiIncorrectEquipmentUsersWithOpcodesGet"])==null?void 0:o[i])==null?void 0:n.url;return(p,h)=>U(c,y,I,r)(p,t||h)}}};class M extends b{apiIncorrectEquipmentCheckSerialNumberByEquipmentSerialNumGet(a,s,e,l){return C(this.configuration).apiIncorrectEquipmentCheckSerialNumberByEquipmentSerialNumGet(a,s,e,l).then(c=>c(this.axios,this.basePath))}apiIncorrectEquipmentCheckServiceIdByServiceIdGet(a,s,e,l){return C(this.configuration).apiIncorrectEquipmentCheckServiceIdByServiceIdGet(a,s,e,l).then(c=>c(this.axios,this.basePath))}apiIncorrectEquipmentGetHistoriesByEquipmentSerialnumberGet(a,s,e,l){return C(this.configuration).apiIncorrectEquipmentGetHistoriesByEquipmentSerialnumberGet(a,s,e,l).then(c=>c(this.axios,this.basePath))}apiIncorrectEquipmentGetUserDisplayNamesByNamePrefixGet(a,s,e,l){return C(this.configuration).apiIncorrectEquipmentGetUserDisplayNamesByNamePrefixGet(a,s,e,l).then(c=>c(this.axios,this.basePath))}apiIncorrectEquipmentTransferCorrectionPost(a,s,e,l){return C(this.configuration).apiIncorrectEquipmentTransferCorrectionPost(a,s,e,l).then(c=>c(this.axios,this.basePath))}apiIncorrectEquipmentTransferDeleteDelete(a,s,e,l){return C(this.configuration).apiIncorrectEquipmentTransferDeleteDelete(a,s,e,l).then(c=>c(this.axios,this.basePath))}apiIncorrectEquipmentUsersWithOpcodesGet(a,s,e){return C(this.configuration).apiIncorrectEquipmentUsersWithOpcodesGet(a,s,e).then(l=>l(this.axios,this.basePath))}}const H=function(r){return{apiSchenkersAddPost:async(a,s,e,l={})=>{u("apiSchenkersAddPost","schenkerRequestModel",a);const c="/api/schenkers/add",i=new URL(c,S);let t;r&&(t=r.baseOptions);const o={method:"POST",...t,...l},n={},p={};n["Content-Type"]="application/json",s!=null&&(n["Input-Request-Id"]=String(s)),e!=null&&(n["Input-Timestamp"]=typeof e=="string"?e:JSON.stringify(e)),O(i,p);let h=t&&t.headers?t.headers:{};return o.headers={...n,...h,...l.headers},o.data=A(a,o,r),{url:V(i),options:o}},apiSchenkersByIdGet:async(a,s,e,l={})=>{u("apiSchenkersByIdGet","id",a);const c="/api/schenkers/{id}".replace("{id}",encodeURIComponent(String(a))),i=new URL(c,S);let t;r&&(t=r.baseOptions);const o={method:"GET",...t,...l},n={},p={};s!=null&&(n["Input-Request-Id"]=String(s)),e!=null&&(n["Input-Timestamp"]=typeof e=="string"?e:JSON.stringify(e)),O(i,p);let h=t&&t.headers?t.headers:{};return o.headers={...n,...h,...l.headers},{url:V(i),options:o}},apiSchenkersSearchPost:async(a,s,e,l={})=>{u("apiSchenkersSearchPost","searchDataRequestModel",a);const c="/api/schenkers/search",i=new URL(c,S);let t;r&&(t=r.baseOptions);const o={method:"POST",...t,...l},n={},p={};n["Content-Type"]="application/json",s!=null&&(n["Input-Request-Id"]=String(s)),e!=null&&(n["Input-Timestamp"]=typeof e=="string"?e:JSON.stringify(e)),O(i,p);let h=t&&t.headers?t.headers:{};return o.headers={...n,...h,...l.headers},o.data=A(a,o,r),{url:V(i),options:o}},apiSchenkersSelectListGet:async(a,s,e={})=>{const l="/api/schenkers/select-list",c=new URL(l,S);let i;r&&(i=r.baseOptions);const t={method:"GET",...i,...e},o={},n={};a!=null&&(o["Input-Request-Id"]=String(a)),s!=null&&(o["Input-Timestamp"]=typeof s=="string"?s:JSON.stringify(s)),O(c,n);let p=i&&i.headers?i.headers:{};return t.headers={...o,...p,...e.headers},{url:V(c),options:t}},apiSchenkersUpdatePut:async(a,s,e,l={})=>{u("apiSchenkersUpdatePut","schenkerRequestModel",a);const c="/api/schenkers/update",i=new URL(c,S);let t;r&&(t=r.baseOptions);const o={method:"PUT",...t,...l},n={},p={};n["Content-Type"]="application/json",s!=null&&(n["Input-Request-Id"]=String(s)),e!=null&&(n["Input-Timestamp"]=typeof e=="string"?e:JSON.stringify(e)),O(i,p);let h=t&&t.headers?t.headers:{};return o.headers={...n,...h,...l.headers},o.data=A(a,o,r),{url:V(i),options:o}},apiSchenkersUsersBySchenkerIdGet:async(a,s,e,l={})=>{u("apiSchenkersUsersBySchenkerIdGet","schenkerId",a);const c="/api/schenkers/users/{schenkerId}".replace("{schenkerId}",encodeURIComponent(String(a))),i=new URL(c,S);let t;r&&(t=r.baseOptions);const o={method:"GET",...t,...l},n={},p={};s!=null&&(n["Input-Request-Id"]=String(s)),e!=null&&(n["Input-Timestamp"]=typeof e=="string"?e:JSON.stringify(e)),O(i,p);let h=t&&t.headers?t.headers:{};return o.headers={...n,...h,...l.headers},{url:V(i),options:o}}}},x=function(r){const a=H(r);return{async apiSchenkersAddPost(s,e,l,c){var n,p;const i=await a.apiSchenkersAddPost(s,e,l,c),t=(r==null?void 0:r.serverIndex)??0,o=(p=(n=P["SchenkersApi.apiSchenkersAddPost"])==null?void 0:n[t])==null?void 0:p.url;return(h,d)=>U(i,y,I,r)(h,o||d)},async apiSchenkersByIdGet(s,e,l,c){var n,p;const i=await a.apiSchenkersByIdGet(s,e,l,c),t=(r==null?void 0:r.serverIndex)??0,o=(p=(n=P["SchenkersApi.apiSchenkersByIdGet"])==null?void 0:n[t])==null?void 0:p.url;return(h,d)=>U(i,y,I,r)(h,o||d)},async apiSchenkersSearchPost(s,e,l,c){var n,p;const i=await a.apiSchenkersSearchPost(s,e,l,c),t=(r==null?void 0:r.serverIndex)??0,o=(p=(n=P["SchenkersApi.apiSchenkersSearchPost"])==null?void 0:n[t])==null?void 0:p.url;return(h,d)=>U(i,y,I,r)(h,o||d)},async apiSchenkersSelectListGet(s,e,l){var o,n;const c=await a.apiSchenkersSelectListGet(s,e,l),i=(r==null?void 0:r.serverIndex)??0,t=(n=(o=P["SchenkersApi.apiSchenkersSelectListGet"])==null?void 0:o[i])==null?void 0:n.url;return(p,h)=>U(c,y,I,r)(p,t||h)},async apiSchenkersUpdatePut(s,e,l,c){var n,p;const i=await a.apiSchenkersUpdatePut(s,e,l,c),t=(r==null?void 0:r.serverIndex)??0,o=(p=(n=P["SchenkersApi.apiSchenkersUpdatePut"])==null?void 0:n[t])==null?void 0:p.url;return(h,d)=>U(i,y,I,r)(h,o||d)},async apiSchenkersUsersBySchenkerIdGet(s,e,l,c){var n,p;const i=await a.apiSchenkersUsersBySchenkerIdGet(s,e,l,c),t=(r==null?void 0:r.serverIndex)??0,o=(p=(n=P["SchenkersApi.apiSchenkersUsersBySchenkerIdGet"])==null?void 0:n[t])==null?void 0:p.url;return(h,d)=>U(i,y,I,r)(h,o||d)}}};class W extends b{apiSchenkersAddPost(a,s,e,l){return x(this.configuration).apiSchenkersAddPost(a,s,e,l).then(c=>c(this.axios,this.basePath))}apiSchenkersByIdGet(a,s,e,l){return x(this.configuration).apiSchenkersByIdGet(a,s,e,l).then(c=>c(this.axios,this.basePath))}apiSchenkersSearchPost(a,s,e,l){return x(this.configuration).apiSchenkersSearchPost(a,s,e,l).then(c=>c(this.axios,this.basePath))}apiSchenkersSelectListGet(a,s,e){return x(this.configuration).apiSchenkersSelectListGet(a,s,e).then(l=>l(this.axios,this.basePath))}apiSchenkersUpdatePut(a,s,e,l){return x(this.configuration).apiSchenkersUpdatePut(a,s,e,l).then(c=>c(this.axios,this.basePath))}apiSchenkersUsersBySchenkerIdGet(a,s,e,l){return x(this.configuration).apiSchenkersUsersBySchenkerIdGet(a,s,e,l).then(c=>c(this.axios,this.basePath))}}const F=function(r){return{apiUsersActivateUsersPut:async(a,s,e,l={})=>{u("apiUsersActivateUsersPut","requestBody",a);const c="/api/users/activate-users",i=new URL(c,S);let t;r&&(t=r.baseOptions);const o={method:"PUT",...t,...l},n={},p={};n["Content-Type"]="application/json",s!=null&&(n["Input-Request-Id"]=String(s)),e!=null&&(n["Input-Timestamp"]=typeof e=="string"?e:JSON.stringify(e)),O(i,p);let h=t&&t.headers?t.headers:{};return o.headers={...n,...h,...l.headers},o.data=A(a,o,r),{url:V(i),options:o}},apiUsersAdAccountByUserIdGet:async(a,s,e,l={})=>{u("apiUsersAdAccountByUserIdGet","userId",a);const c="/api/users/ad-account/{userId}".replace("{userId}",encodeURIComponent(String(a))),i=new URL(c,S);let t;r&&(t=r.baseOptions);const o={method:"GET",...t,...l},n={},p={};s!=null&&(n["Input-Request-Id"]=String(s)),e!=null&&(n["Input-Timestamp"]=typeof e=="string"?e:JSON.stringify(e)),O(i,p);let h=t&&t.headers?t.headers:{};return o.headers={...n,...h,...l.headers},{url:V(i),options:o}},apiUsersAllMolsGet:async(a,s,e={})=>{const l="/api/users/all-mols",c=new URL(l,S);let i;r&&(i=r.baseOptions);const t={method:"GET",...i,...e},o={},n={};a!=null&&(o["Input-Request-Id"]=String(a)),s!=null&&(o["Input-Timestamp"]=typeof s=="string"?s:JSON.stringify(s)),O(c,n);let p=i&&i.headers?i.headers:{};return t.headers={...o,...p,...e.headers},{url:V(c),options:t}},apiUsersBlockUsersPut:async(a,s,e,l={})=>{u("apiUsersBlockUsersPut","requestBody",a);const c="/api/users/block-users",i=new URL(c,S);let t;r&&(t=r.baseOptions);const o={method:"PUT",...t,...l},n={},p={};n["Content-Type"]="application/json",s!=null&&(n["Input-Request-Id"]=String(s)),e!=null&&(n["Input-Timestamp"]=typeof e=="string"?e:JSON.stringify(e)),O(i,p);let h=t&&t.headers?t.headers:{};return o.headers={...n,...h,...l.headers},o.data=A(a,o,r),{url:V(i),options:o}},apiUsersByUserIdGet:async(a,s,e,l={})=>{u("apiUsersByUserIdGet","userId",a);const c="/api/users/{userId}".replace("{userId}",encodeURIComponent(String(a))),i=new URL(c,S);let t;r&&(t=r.baseOptions);const o={method:"GET",...t,...l},n={},p={};s!=null&&(n["Input-Request-Id"]=String(s)),e!=null&&(n["Input-Timestamp"]=typeof e=="string"?e:JSON.stringify(e)),O(i,p);let h=t&&t.headers?t.headers:{};return o.headers={...n,...h,...l.headers},{url:V(i),options:o}},apiUsersIsUserMolByUserIdGet:async(a,s,e,l={})=>{u("apiUsersIsUserMolByUserIdGet","userId",a);const c="/api/users/is-user-mol/{userId}".replace("{userId}",encodeURIComponent(String(a))),i=new URL(c,S);let t;r&&(t=r.baseOptions);const o={method:"GET",...t,...l},n={},p={};s!=null&&(n["Input-Request-Id"]=String(s)),e!=null&&(n["Input-Timestamp"]=typeof e=="string"?e:JSON.stringify(e)),O(i,p);let h=t&&t.headers?t.headers:{};return o.headers={...n,...h,...l.headers},{url:V(i),options:o}},apiUsersPut:async(a,s,e,l={})=>{u("apiUsersPut","editUserRequest",a);const c="/api/users",i=new URL(c,S);let t;r&&(t=r.baseOptions);const o={method:"PUT",...t,...l},n={},p={};n["Content-Type"]="application/json",s!=null&&(n["Input-Request-Id"]=String(s)),e!=null&&(n["Input-Timestamp"]=typeof e=="string"?e:JSON.stringify(e)),O(i,p);let h=t&&t.headers?t.headers:{};return o.headers={...n,...h,...l.headers},o.data=A(a,o,r),{url:V(i),options:o}},apiUsersSearchPost:async(a,s,e,l={})=>{u("apiUsersSearchPost","searchDataRequestModel",a);const c="/api/users/search",i=new URL(c,S);let t;r&&(t=r.baseOptions);const o={method:"POST",...t,...l},n={},p={};n["Content-Type"]="application/json",s!=null&&(n["Input-Request-Id"]=String(s)),e!=null&&(n["Input-Timestamp"]=typeof e=="string"?e:JSON.stringify(e)),O(i,p);let h=t&&t.headers?t.headers:{};return o.headers={...n,...h,...l.headers},o.data=A(a,o,r),{url:V(i),options:o}},apiUsersUserByUsernameByUsernameGet:async(a,s,e,l={})=>{u("apiUsersUserByUsernameByUsernameGet","username",a);const c="/api/users/user-by-username/{username}".replace("{username}",encodeURIComponent(String(a))),i=new URL(c,S);let t;r&&(t=r.baseOptions);const o={method:"GET",...t,...l},n={},p={};s!=null&&(n["Input-Request-Id"]=String(s)),e!=null&&(n["Input-Timestamp"]=typeof e=="string"?e:JSON.stringify(e)),O(i,p);let h=t&&t.headers?t.headers:{};return o.headers={...n,...h,...l.headers},{url:V(i),options:o}},apiUsersUsersSelectListGet:async(a,s,e={})=>{const l="/api/users/users-select-list",c=new URL(l,S);let i;r&&(i=r.baseOptions);const t={method:"GET",...i,...e},o={},n={};a!=null&&(o["Input-Request-Id"]=String(a)),s!=null&&(o["Input-Timestamp"]=typeof s=="string"?s:JSON.stringify(s)),O(c,n);let p=i&&i.headers?i.headers:{};return t.headers={...o,...p,...e.headers},{url:V(c),options:t}}}},B=function(r){const a=F(r);return{async apiUsersActivateUsersPut(s,e,l,c){var n,p;const i=await a.apiUsersActivateUsersPut(s,e,l,c),t=(r==null?void 0:r.serverIndex)??0,o=(p=(n=P["UsersApi.apiUsersActivateUsersPut"])==null?void 0:n[t])==null?void 0:p.url;return(h,d)=>U(i,y,I,r)(h,o||d)},async apiUsersAdAccountByUserIdGet(s,e,l,c){var n,p;const i=await a.apiUsersAdAccountByUserIdGet(s,e,l,c),t=(r==null?void 0:r.serverIndex)??0,o=(p=(n=P["UsersApi.apiUsersAdAccountByUserIdGet"])==null?void 0:n[t])==null?void 0:p.url;return(h,d)=>U(i,y,I,r)(h,o||d)},async apiUsersAllMolsGet(s,e,l){var o,n;const c=await a.apiUsersAllMolsGet(s,e,l),i=(r==null?void 0:r.serverIndex)??0,t=(n=(o=P["UsersApi.apiUsersAllMolsGet"])==null?void 0:o[i])==null?void 0:n.url;return(p,h)=>U(c,y,I,r)(p,t||h)},async apiUsersBlockUsersPut(s,e,l,c){var n,p;const i=await a.apiUsersBlockUsersPut(s,e,l,c),t=(r==null?void 0:r.serverIndex)??0,o=(p=(n=P["UsersApi.apiUsersBlockUsersPut"])==null?void 0:n[t])==null?void 0:p.url;return(h,d)=>U(i,y,I,r)(h,o||d)},async apiUsersByUserIdGet(s,e,l,c){var n,p;const i=await a.apiUsersByUserIdGet(s,e,l,c),t=(r==null?void 0:r.serverIndex)??0,o=(p=(n=P["UsersApi.apiUsersByUserIdGet"])==null?void 0:n[t])==null?void 0:p.url;return(h,d)=>U(i,y,I,r)(h,o||d)},async apiUsersIsUserMolByUserIdGet(s,e,l,c){var n,p;const i=await a.apiUsersIsUserMolByUserIdGet(s,e,l,c),t=(r==null?void 0:r.serverIndex)??0,o=(p=(n=P["UsersApi.apiUsersIsUserMolByUserIdGet"])==null?void 0:n[t])==null?void 0:p.url;return(h,d)=>U(i,y,I,r)(h,o||d)},async apiUsersPut(s,e,l,c){var n,p;const i=await a.apiUsersPut(s,e,l,c),t=(r==null?void 0:r.serverIndex)??0,o=(p=(n=P["UsersApi.apiUsersPut"])==null?void 0:n[t])==null?void 0:p.url;return(h,d)=>U(i,y,I,r)(h,o||d)},async apiUsersSearchPost(s,e,l,c){var n,p;const i=await a.apiUsersSearchPost(s,e,l,c),t=(r==null?void 0:r.serverIndex)??0,o=(p=(n=P["UsersApi.apiUsersSearchPost"])==null?void 0:n[t])==null?void 0:p.url;return(h,d)=>U(i,y,I,r)(h,o||d)},async apiUsersUserByUsernameByUsernameGet(s,e,l,c){var n,p;const i=await a.apiUsersUserByUsernameByUsernameGet(s,e,l,c),t=(r==null?void 0:r.serverIndex)??0,o=(p=(n=P["UsersApi.apiUsersUserByUsernameByUsernameGet"])==null?void 0:n[t])==null?void 0:p.url;return(h,d)=>U(i,y,I,r)(h,o||d)},async apiUsersUsersSelectListGet(s,e,l){var o,n;const c=await a.apiUsersUsersSelectListGet(s,e,l),i=(r==null?void 0:r.serverIndex)??0,t=(n=(o=P["UsersApi.apiUsersUsersSelectListGet"])==null?void 0:o[i])==null?void 0:n.url;return(p,h)=>U(c,y,I,r)(p,t||h)}}};class z extends b{apiUsersActivateUsersPut(a,s,e,l){return B(this.configuration).apiUsersActivateUsersPut(a,s,e,l).then(c=>c(this.axios,this.basePath))}apiUsersAdAccountByUserIdGet(a,s,e,l){return B(this.configuration).apiUsersAdAccountByUserIdGet(a,s,e,l).then(c=>c(this.axios,this.basePath))}apiUsersAllMolsGet(a,s,e){return B(this.configuration).apiUsersAllMolsGet(a,s,e).then(l=>l(this.axios,this.basePath))}apiUsersBlockUsersPut(a,s,e,l){return B(this.configuration).apiUsersBlockUsersPut(a,s,e,l).then(c=>c(this.axios,this.basePath))}apiUsersByUserIdGet(a,s,e,l){return B(this.configuration).apiUsersByUserIdGet(a,s,e,l).then(c=>c(this.axios,this.basePath))}apiUsersIsUserMolByUserIdGet(a,s,e,l){return B(this.configuration).apiUsersIsUserMolByUserIdGet(a,s,e,l).then(c=>c(this.axios,this.basePath))}apiUsersPut(a,s,e,l){return B(this.configuration).apiUsersPut(a,s,e,l).then(c=>c(this.axios,this.basePath))}apiUsersSearchPost(a,s,e,l){return B(this.configuration).apiUsersSearchPost(a,s,e,l).then(c=>c(this.axios,this.basePath))}apiUsersUserByUsernameByUsernameGet(a,s,e,l){return B(this.configuration).apiUsersUserByUsernameByUsernameGet(a,s,e,l).then(c=>c(this.axios,this.basePath))}apiUsersUsersSelectListGet(a,s,e){return B(this.configuration).apiUsersUsersSelectListGet(a,s,e).then(l=>l(this.axios,this.basePath))}}export{D as A,$ as C,M as I,W as S,z as U};
