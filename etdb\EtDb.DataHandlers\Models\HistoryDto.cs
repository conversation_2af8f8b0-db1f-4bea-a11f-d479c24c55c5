﻿namespace EtDb.DataHandlers.Models
{
    public class HistoryDto
    {
        public int Id { get; set; }
        public int ItemId { get; set; }
        public int ItemTypeOfUsage { get; set; }
        public string ItemValidity { get; set; }
        public string IcmIdSgwId { get; set; }
        public string SourceSystem { get; set; }
        public string DeliveryType { get; set; }
        public string DelivelyFromPoint { get; set; }
        public string DeliveryShop { get; set; }
        public string FromUserId { get; set; }
        public string ToUserId { get; set; }
        public string DeliveryNumSap { get; set; }
        public DateTime? DeliveryDateSap { get; set; }
        public int DeliveryItemQty { get; set; }
        public string IntDeliveryNum { get; set; }
        public DateTime? InsertDate { get; set; }
        public int OperationType { get; set; }
        public int DocStatus { get; set; }
        public string ServiceIdFrom { get; set; }
        public string ServiceIdTo { get; set; }
        public int? OrderType { get; set; }
        public string CrmorderId { get; set; }
        public int? RefuseReason { get; set; }
        public int? AutoGenerated { get; set; }
        public int? OrderAction { get; set; }
        public int? PostOfficeId { get; set; }
        public DateTime? WaybillDate { get; set; }
        public string WaybillNum { get; set; }
        public long? TransferNumber { get; set; }
        public virtual ItemDto Item { get; set; }
    }
}
