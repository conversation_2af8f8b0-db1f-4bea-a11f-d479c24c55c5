﻿namespace EtWS.Api.Infrastructure.Mapper
{
    using EtWS.ApiClients.ETDB;
    using EtWS.Models.Requests;
    using EtWS.Models.Responses.CommonModels;
    using EtWS.Models.Responses.EquipmentModels;

    public class EquipmentProfile : Profile
    {
        public EquipmentProfile()
        {
            this.CreateMap<SearchDataRequestModel, SearchRequestModelWithUserId>();

            this.CreateMap<AvailableEquipmentResponseModelSearchResponseModel, SearchResponseModel<AvailableEquipmentDataResponseModel>>();

            this.CreateMap<AvailableEquipmentResponseModel, AvailableEquipmentDataResponseModel>();

            this.CreateMap<ItemResponseModel, ItemDataResponseModel>();

            this.CreateMap<ReserveItemForTransferDataRequestModel, ReserveItemForTransferRequestModel>();

            this.CreateMap<ReserveItemHistoryResponseModel, ReserveItemHistoryDataResponseModel>();

            this.CreateMap<SearchDataRequestModel, SearchRequestModelWithUserId>();

            this.CreateMap<ItemResponseModelSearchResponseModel, SearchResponseModel<ItemDataResponseModel>>();

            this.CreateMap<UserItemsToAcceptResponseModelSearchResponseModel, SearchResponseModel<UserItemsToAcceptResponseModel>>();

            this.CreateMap<UserItemsToCancelResponseModelSearchResponseModel, SearchResponseModel<UserItemsToCancelResponseModel>>();

            this.CreateMap<AcceptItemsForTransferRequestModel, AcceptItemsRequestModel>();

            this.CreateMap<RefuseItemsForTransferRequestModel, RefuseItemsRequestModel>();

            this.CreateMap<CancelItemsForTransferRequestModel, CancelItemsRequestModel>();

            this.CreateMap<DeliverItemsDataRequestModel, DeliverItemsRequestModel>();
        }
    }
}
