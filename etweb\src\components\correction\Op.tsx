import { useFormContext } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { useGetOpQuery } from "../../data/query";
import { useEffect, useMemo } from "react";
import { Label } from "@/components/ui/label";
import { XCircle } from "lucide-react";
import { FilteredDropdown, type DropdownOption } from "@/components/ui/filtered-dropdown";

const Op = () => {
  const { data: opOptions, isLoading, error } = useGetOpQuery();
  const { t } = useTranslation();
  const {
    watch,
    setValue,
    formState: { errors },
  } = useFormContext();

  const hasError = !!errors.opId;
  const errorMessage = errors.opId?.message?.toString();

  const userId = watch("userId");
  const selectedOption = opOptions?.find((option) => option.id === userId);

  // Convert opOptions to DropdownOption format
  const dropdownOptions: DropdownOption[] = useMemo(() => {
    if (!opOptions) return [];

    return opOptions.map((option) => ({
      value: option.id || "",
      label: `${option.displayName} - ${option.opcode}`,
      group: option.iptuName || "Other",
      // Store original option data for later use
      originalData: option
    }));
  }, [opOptions]);

  useEffect(() => {
    if (selectedOption?.opcode) {
      setValue("deliveryShop", selectedOption.opcode);
    }
  }, [selectedOption, setValue]);

  return (
    <div className="space-y-2">
      <Label htmlFor="opId">{t("selectATechnicianFromTheList")}</Label>
      <div>
        {isLoading ? (
          <div className="flex items-center space-x-2 py-3">
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-indigo-500 border-t-transparent"></div>
            <span className="text-sm text-gray-500">{t("loading")}</span>
          </div>
        ) : error ? (
          <div className="flex items-center rounded-lg border border-red-200 bg-red-50 px-4 py-3 text-sm text-destructive">
            <XCircle className="mr-2 h-5 w-5" />
            <span>{error.message}</span>
          </div>
        ) : (
          <FilteredDropdown
            options={dropdownOptions}
            value={userId || ""}
            onValueChange={(value) => setValue("userId", value)}
            placeholder={t("selectOperation")}
            groupBy="group"
            clearable
            showSearch={true}
            searchPlaceholder={t("search", "Search...")}
            emptyText={t("noOptionsFound", "No options found")}
          />
        )}
        {hasError && !isLoading && !error && (
          <div className="mt-1.5 flex items-center text-sm text-destructive">
            <XCircle className="mr-1.5 h-4 w-4" />
            <p>{t(errorMessage || "")}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Op;
