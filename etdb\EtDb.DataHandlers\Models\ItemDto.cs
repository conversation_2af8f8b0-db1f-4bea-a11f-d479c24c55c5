﻿namespace EtDb.DataHandlers.Models
{
    public class ItemDto
    {
        public int Id { get; set; }
        public string IcmIdSgwId { get; set; }
        public string SourceSystem { get; set; }
        public DateTime? ModifyDate { get; set; }
        public string EquipmentName { get; set; }
        public string EquipmentSerialNum { get; set; }
        public string SapserialNum { get; set; }
        public string SapmaterialNum { get; set; }
        public int EquipmentTypeId { get; set; }
        public string EquipmentMaterialGroup { get; set; }
        public int TypeOfUsage { get; set; }
        public string EquipmentValidity { get; set; }
        public DateTime? AdditionDate { get; set; }
    }
}
