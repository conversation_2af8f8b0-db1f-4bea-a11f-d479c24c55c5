﻿namespace EtWS.Infrastructure.Constants
{
    public static class GlobalConstants
    {
        public const string CentralWarehouse = "centralWarehouse";
        public const string SourceSystem = ".NET";
        public const string CentralWarehouseDeliveryType = "ZNL";
        public const int CentralWarehouseOperationType = 1;
        public const int AutoGeneratedValue = 0;
        public const int CentralWarehouseDocStatus = 6;
        public const string CentralWarehouseToUserId = "d0df1fe4-5063-4a04-b224-d4c708825305";

        //Constants to technician
        public const string Technician = "technician";
        public const string TechniciaDeliveryType = "INT";
        public const int TechniciaOperationType = 2;
        public const int TechniciaDocStatus = 3;

        // Constants to SAP virtual
        public const string SAPVirtual = "SAPVirtualWarehouse";
        public const string SAPVirtalDeliveryType = "INT";
        public const int SAPVirtalOperationType = 2;
        public const int SAPVirtalDocStatus = 3;
        public const int SAPVirtalQuantity = 1;

        // Constants to client
        public const string Client = "client";
        public const string ClientDeliveryType = "0";
        public const int ClientOperationType = 3;
        public const int ClientDocStatus = 7;

        // Constants to OP
        public const string OP = "op";

        public const string NotAuthorizedMessage = "Not authorized";
        public const string WrongPasswordOrUsernameMessage = "Wrong password or username";

        public const string SecretKey = "ETWSETWSAPIAUTHENTICATION";

        // Constants for notification types
        public const string PendingTransfer = "PendingTransfer";
        public const string AcceptanceOfTransfer = "AcceptanceOfTransfer";
        public const string UnacceptedTransfer = "UnacceptedTransfer";
        public const string CanceledTransfer = "CanceledTransfer";

        // Transfer action names
        public const string AcceptItemsActionName = "AcceptItems";
        public const string RefuseItemsActionName = "RefuseItems";
        public const string CancelItemsActionName = "CancelItems";

        // Category constants
        public const string MostFrequentCategory = "Най-често избирани...";
        public const string OtherCathegory = "Други";

        public const string SpecialUserEln = "99999";
    }
}
