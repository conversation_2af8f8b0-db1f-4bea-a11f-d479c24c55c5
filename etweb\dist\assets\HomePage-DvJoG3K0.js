import{j as e}from"./query-vendor-DbpRcGHB.js";import{u as s}from"./i18n-vendor-CzM1WOJE.js";import"./router-vendor-CX6yTN5-.js";import"./react-vendor-DJG_os-6.js";function x(){const{t}=s();return e.jsxs("div",{className:"flex items-center justify-center flex-col mt-64",children:[e.jsxs("h1",{className:"text-3xl sm:text-4xl md:text-5xl font-bold group flex items-center justify-center text-center",children:[e.jsx("span",{children:t("welcome")}),e.jsx("span",{className:"text-blue-500 group-hover:text-blue-700 transition-colors duration-300",children:t("ET")})]}),e.jsx("div",{className:"flex items-center justify-center mt-5",children:e.jsx("img",{className:"w-full max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg h-auto",src:"/et-removebg-preview.png",alt:"ET"})})]})}export{x as default};
