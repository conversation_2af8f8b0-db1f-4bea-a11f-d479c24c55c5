import { useState, lazy, Suspense } from "react";
import { useTranslation } from "react-i18next";
import { User<PERSON>he<PERSON>, UserX } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";

import { Dialog, DialogContent } from "@/components/ui/dialog";
import {
  TableFilter,
  type ColumnFilter,
  type TableColumn,
} from "@/components/ui/TableFilter";
import { TablePagination } from "@/components/ui/TablePagination";
import {
  useUsersSearch,
  useActivateUsers,
  useBlockUsers,
} from "@/hooks/useUsers";
import type { SearchUserDataResponseModel } from "@/data/etws";
import { useSchenkersSelectList } from "@/hooks/useSchenkers";
import { ErrorState } from "@/components/ui/error-state";

// Lazy load heavy components
const UserEditDialog = lazy(() => import("@/components/administration/UserEditDialog").then(module => ({ default: module.UserEditDialog })));
const UserTable = lazy(() => import("@/components/administration/UserTable"));

export default function UsersPage() {
  const { t } = useTranslation();
  const [columnFilters, setColumnFilters] = useState<ColumnFilter[]>([]);
  const [queryString, setQueryString] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [editingUserId, setEditingUserId] = useState<string | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [sortBy, setSortBy] = useState("Eln");
  const [sortDir, setSortDir] = useState("asc");
  const { data: schenkersData } = useSchenkersSelectList();

  // Define table columns for filtering
  const tableColumns: TableColumn[] = [
    { field: "Eln", label: t("eln") },
    { field: "FirstName", label: t("firstName") },
    { field: "Surname", label: t("surname") },
    {
      field: "FamilyName",
      label: t("familyName"),
    },
    {
      field: "SchenkerId",
      label: t("op"),
      type: "select",
      options:
        schenkersData?.map((schenker) => ({
          value: schenker.id?.toString() || "",
          label: schenker.opCode ?? "",
        })) || [],
    },
    {
      field: "City.Region",
      label: t("region"),
      type: "select",
      options: [
        { value: "1", label: "Север" },
        { value: "2", label: "Юг" },
        { value: "3", label: "Изток" },
        { value: "4", label: "Запад" },
        { value: "5", label: "София" },
      ],
    },
    { field: "IPTUName", label: t("iptuName") },
    { field: "City.Name", label: t("city") },
    {
      field: "Blocked",
      label: t("status"),
      type: "select",
      options: [
        { value: "0", label: t("active") },
        { value: "1", label: t("blocked") },
      ],
    },
  ];

  // Define sort options
  const sortOptions = [
    { value: "Eln", label: t("eln") },
    { value: "FirstName", label: t("firstName") },
    { value: "Surname", label: t("surname") },
    { value: "FamilyName", label: t("familyName") },
    { value: "SchenkerId", label: t("op") },
    { value: "City.Region", label: t("region") },
    { value: "IPTUName", label: t("iptuName") },
    { value: "City.Name", label: t("city") },
    { value: "ClientNumber", label: t("clientNumber") },
    { value: "Blocked", label: t("status") },
  ];

  const searchParams = {
    pageNumber: currentPage,
    pageSize,
    query: queryString,
    sortBy,
    sortDir,
  };

  const {
    data: usersData,
    isLoading,
    error,
    refetch: refetchUsers,
  } = useUsersSearch(searchParams);
  const activateUsersMutation = useActivateUsers();
  const blockUsersMutation = useBlockUsers();

  const users = usersData?.dataCollection || [];
  const totalCount = usersData?.count || 0;
  const totalPages = Math.ceil(totalCount / pageSize);

  const handleSelectUser = (userId: string, checked: boolean) => {
    if (checked) {
      setSelectedUsers((prev) => [...prev, userId]);
    } else {
      setSelectedUsers((prev) => prev.filter((id) => id !== userId));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedUsers(users.map((user) => user.id || ""));
    } else {
      setSelectedUsers([]);
    }
  };

  const handleEditUser = (user: SearchUserDataResponseModel) => {
    setEditingUserId(user.id || "");
    setIsEditDialogOpen(true);
  };

  const handleActivateSelected = () => {
    if (selectedUsers.length > 0) {
      activateUsersMutation.mutate(selectedUsers, {
        onSuccess: () => {
          setSelectedUsers([]);
        },
      });
    }
  };

  const handleBlockSelected = () => {
    if (selectedUsers.length > 0) {
      blockUsersMutation.mutate(selectedUsers, {
        onSuccess: () => {
          setSelectedUsers([]);
        },
      });
    }
  };

  if (error) {
    return (
      <ErrorState
        title={t("errorLoadingUsers")}
        onRetry={() => refetchUsers()}
      />
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">{t("users")}</h1>
          <p className="text-muted-foreground">{t("manageSystemUsers")}</p>
        </div>
      </div>

      {/* Filters and Actions */}
      <div className="space-y-4">
        <div className="flex items-center justify-end gap-2">
          {selectedUsers.length > 0 && (
            <>
              <Button
                variant="outline"
                size="sm"
                onClick={handleActivateSelected}
                disabled={activateUsersMutation.isPending}
              >
                <UserCheck className="h-4 w-4 mr-2" />
                {t("activate")} ({selectedUsers.length})
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleBlockSelected}
                disabled={blockUsersMutation.isPending}
              >
                <UserX className="h-4 w-4 mr-2" />
                {t("block")} ({selectedUsers.length})
              </Button>
            </>
          )}
        </div>
        <TableFilter
          columns={tableColumns}
          filters={columnFilters}
          onFiltersChange={setColumnFilters}
          onQueryChange={setQueryString}
          sortBy={sortBy}
          sortDir={sortDir}
          onSortChange={(newSortBy, newSortDir) => {
            setSortBy(newSortBy);
            setSortDir(newSortDir);
          }}
          sortOptions={sortOptions}
        />
      </div>

      {/* Users Table */}
      <Suspense fallback={<div className="flex items-center justify-center p-8"><div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div></div>}>
        <UserTable
          users={users}
          isLoading={isLoading}
          selectedUsers={selectedUsers}
          handleSelectUser={handleSelectUser}
          handleSelectAll={handleSelectAll}
          handleEditUser={handleEditUser}
        />
      </Suspense>

      {/* Pagination */}
      <TablePagination
        currentPage={currentPage}
        totalPages={totalPages}
        pageSize={pageSize}
        totalCount={totalCount}
        onPageChange={setCurrentPage}
        onPageSizeChange={(newPageSize) => {
          setPageSize(newPageSize);
          setCurrentPage(1); // Reset to first page when changing page size
        }}
        showPageSizeSelector={true}
        showResultsInfo={true}
        pageSizeOptions={[10, 20, 50, 100]}
      />

      {/* Edit User Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          {editingUserId && (
            <Suspense fallback={<div className="flex items-center justify-center p-8"><div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div></div>}>
              <UserEditDialog
                userId={editingUserId}
                onClose={() => {
                  setIsEditDialogOpen(false);
                  setEditingUserId(null);
                }}
              />
            </Suspense>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
