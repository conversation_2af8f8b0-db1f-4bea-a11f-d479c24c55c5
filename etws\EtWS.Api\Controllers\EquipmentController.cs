﻿namespace EtWS.Api.Controllers
{
    using EtWS.Api.Infrastructure.Utils;
    using EtWS.ApiClients.ETDB;
    using EtWS.Models.Requests;
    using EtWS.Models.Responses.CommonModels;
    using EtWS.Models.Responses.EquipmentModels;
    using EtWS.Services.EquipmentService;
    using EtWS.Services.NotificationsService;
    using Microsoft.AspNetCore.Authentication.Cookies;
    using Microsoft.AspNetCore.Authorization;

    [Authorize(AuthenticationSchemes = CookieAuthenticationDefaults.AuthenticationScheme)]
    public class EquipmentController : BaseController
    {
        private readonly Lazy<IEquipmentService> equipmentService;

        public EquipmentController(Lazy<IEquipmentService> equipmentService)
        {
            this.equipmentService = equipmentService;
        }

        [HttpPost("search-available-equipment")]
        [ProducesResponseType(typeof(SearchResponseModel<AvailableEquipmentDataResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<SearchResponseModel<AvailableEquipmentDataResponseModel>>> SearchAvailableEquipmentAsync([Required] SearchDataRequestModel request)
        {
            try
            {
                if (this.ModelState.IsValid)
                {
                    var result = await this.equipmentService.Value.SearchAvailableEquipmentAsync(request, this.GetUserId());
                    return this.Ok(result);
                }
                else
                {
                    var requestErrorMessage = this.ModelState.GetFirstModelErrorMessage();
                    return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = requestErrorMessage });
                }
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPost("reserve-item")]
        [ProducesResponseType(typeof(ReserveItemHistoryDataResponseModel), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<ReserveItemHistoryDataResponseModel>> ReserveItemForTransferAsync([Required] ReserveItemForTransferDataRequestModel request)
        {
            try
            {
                if (!string.IsNullOrEmpty(request.ItemSerialNumber))
                {
                    request.ItemSerialNumber = request.ItemSerialNumber.Replace(" ", string.Empty);
                }

                return this.Ok(await this.equipmentService.Value.ReserveItemForTransferAsync(request, this.GetUserId()));
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPost("reserve-all-items")]
        [ProducesResponseType(typeof(int), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<int>> ReserveAllItemsForTransferAsync()
        {
            try
            {
                return this.Ok(await this.equipmentService.Value.ReserveAllItemsForTransferAsync(this.GetUserId()));
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpGet("reserved-items-count")]
        [ProducesResponseType(typeof(int), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<int>> CountAllUserReservedItemsForTransfer()
        {
            try
            {
                return this.Ok(await this.equipmentService.Value.CountAllUserReservedItemsForTransfer(this.GetUserId()));
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPost("remove-item")]
        [ProducesResponseType(typeof(RemoveItemFromTransferResponseModel), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<RemoveItemFromTransferResponseModel>> RemoveItemFromTransferAsync([Required] int itemId)
        {
            try
            {
                return this.Ok(await this.equipmentService.Value.RemoveItemFromTransferAsync(itemId, this.GetUserId()));
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPost("remove-items")]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> RemoveItemsFromTransferAsync([Required] IEnumerable<int> selectedItems)
        {
            try
            {
                await this.equipmentService.Value.RemoveItemsFromTransferAsync(selectedItems, this.GetUserId());
                return this.Ok();
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPost("remove-all-items")]
        [ProducesResponseType(typeof(int), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<int>> RemoveAllItemsFromTransferAsync()
        {
            try
            {
                return this.Ok(await this.equipmentService.Value.RemoveAllItemsFromTransferAsync(this.GetUserId()));
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPost("search-transfer-data")]
        [ProducesResponseType(typeof(SearchResponseModel<ItemDataResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<SearchResponseModel<ItemDataResponseModel>>> SearchTransferDataAsync([Required] SearchDataRequestModel request)
        {
            try
            {
                if (this.ModelState.IsValid)
                {
                    var result = await this.equipmentService.Value.SearchTransferDataAsync(request, this.GetUserId());
                    return this.Ok(result);
                }
                else
                {
                    var requestErrorMessage = this.ModelState.GetFirstModelErrorMessage();
                    return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = requestErrorMessage });
                }
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpGet("post-offices-select-list")]
        [ProducesResponseType(typeof(IEnumerable<PostOfficesSelectListResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<IEnumerable<PostOfficesSelectListResponseModel>>> GetAllActivePostOfficesAsync()
        {
            try
            {
                return this.Ok(await this.equipmentService.Value.GetAllActivePostOfficesAsync());
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPost("deliver-items")]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> DeliverItemsAsync([Required] DeliverItemsDataRequestModel request)
        {
            try
            {
                if (this.ModelState.IsValid)
                {
                    await this.equipmentService.Value.DeliverItemsAsync(request, this.GetUserId());
                    return this.Ok();
                }
                else
                {
                    var requestErrorMessage = this.ModelState.GetFirstModelErrorMessage();
                    return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = requestErrorMessage });
                }
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPost("search-user-items-to-accept")]
        [ProducesResponseType(typeof(SearchResponseModel<UserItemsToAcceptResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<SearchResponseModel<UserItemsToAcceptResponseModel>>> SearchUserItemsToAcceptDataAsync([Required] SearchDataRequestModel request)
        {
            try
            {
                if (this.ModelState.IsValid)
                {
                    var result = await this.equipmentService.Value.SearchUserItemsToAcceptDataAsync(request, this.GetUserId());
                    return this.Ok(result);
                }
                else
                {
                    var requestErrorMessage = this.ModelState.GetFirstModelErrorMessage();
                    return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = requestErrorMessage });
                }
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPost("search-user-items-to-cancel")]
        [ProducesResponseType(typeof(SearchResponseModel<UserItemsToCancelResponseModel>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<SearchResponseModel<UserItemsToCancelResponseModel>>> SearchUserItemsToCancelDataAsync([Required] SearchDataRequestModel request)
        {
            try
            {
                if (this.ModelState.IsValid)
                {
                    var result = await this.equipmentService.Value.SearchUserItemsToCancelDataAsync(request, this.GetUserId());
                    return this.Ok(result);
                }
                else
                {
                    var requestErrorMessage = this.ModelState.GetFirstModelErrorMessage();
                    return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = requestErrorMessage });
                }
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPost("accept-items")]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> AcceptItemsAsync([Required] AcceptItemsForTransferRequestModel request)
        {
            try
            {
                if (this.ModelState.IsValid)
                {
                    await this.equipmentService.Value.AcceptItemsAsync(request, this.GetUserId());
                    return this.Ok();
                }
                else
                {
                    var requestErrorMessage = this.ModelState.GetFirstModelErrorMessage();
                    return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = requestErrorMessage });
                }
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPost("refuse-items")]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> RefuseItemsAsync([Required] RefuseItemsForTransferRequestModel request)
        {
            try
            {
                if (this.ModelState.IsValid)
                {
                    await this.equipmentService.Value.RefuseItemsAsync(request, this.GetUserId());
                    return this.Ok();
                }
                else
                {
                    var requestErrorMessage = this.ModelState.GetFirstModelErrorMessage();
                    return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = requestErrorMessage });
                }
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }

        [HttpPost("cancel-items")]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponseModel), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> CancelItemsAsync([Required] CancelItemsForTransferRequestModel request)
        {
            try
            {
                if (this.ModelState.IsValid)
                {
                    await this.equipmentService.Value.CancelItemsAsync(request, this.GetUserId());
                    return this.Ok();
                }
                else
                {
                    var requestErrorMessage = this.ModelState.GetFirstModelErrorMessage();
                    return this.StatusCode(StatusCodes.Status400BadRequest, new BaseResponseModel { Success = false, Message = requestErrorMessage });
                }
            }
            catch (Exception ex)
            {
                return ExceptionHandler.HandleException(ex);
            }
        }
    }
}
