import{r as Oe,g as Ie}from"./react-vendor-DJG_os-6.js";function Ne(e,t){for(var r=0;r<t.length;r++){const a=t[r];if(typeof a!="string"&&!Array.isArray(a)){for(const n in a)if(n!=="default"&&!(n in e)){const o=Object.getOwnPropertyDescriptor(a,n);o&&Object.defineProperty(e,n,o.get?o:{enumerable:!0,get:()=>a[n]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}var u=Oe();const Te=Ie(u),lr=Ne({__proto__:null,default:Te},[u]);var B={},se;function Fe(){if(se)return B;se=1,Object.defineProperty(B,"__esModule",{value:!0}),B.parse=s,B.serialize=i;const e=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,t=/^[\u0021-\u003A\u003C-\u007E]*$/,r=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,a=/^[\u0020-\u003A\u003D-\u007E]*$/,n=Object.prototype.toString,o=(()=>{const d=function(){};return d.prototype=Object.create(null),d})();function s(d,v){const f=new o,y=d.length;if(y<2)return f;const w=(v==null?void 0:v.decode)||m;let h=0;do{const g=d.indexOf("=",h);if(g===-1)break;const x=d.indexOf(";",h),R=x===-1?y:x;if(g>R){h=d.lastIndexOf(";",g-1)+1;continue}const C=c(d,h,g),F=l(d,g,C),I=d.slice(C,F);if(f[I]===void 0){let T=c(d,g+1,R),b=l(d,R,T);const N=w(d.slice(T,b));f[I]=N}h=R+1}while(h<y);return f}function c(d,v,f){do{const y=d.charCodeAt(v);if(y!==32&&y!==9)return v}while(++v<f);return f}function l(d,v,f){for(;v>f;){const y=d.charCodeAt(--v);if(y!==32&&y!==9)return v+1}return f}function i(d,v,f){const y=(f==null?void 0:f.encode)||encodeURIComponent;if(!e.test(d))throw new TypeError(`argument name is invalid: ${d}`);const w=y(v);if(!t.test(w))throw new TypeError(`argument val is invalid: ${v}`);let h=d+"="+w;if(!f)return h;if(f.maxAge!==void 0){if(!Number.isInteger(f.maxAge))throw new TypeError(`option maxAge is invalid: ${f.maxAge}`);h+="; Max-Age="+f.maxAge}if(f.domain){if(!r.test(f.domain))throw new TypeError(`option domain is invalid: ${f.domain}`);h+="; Domain="+f.domain}if(f.path){if(!a.test(f.path))throw new TypeError(`option path is invalid: ${f.path}`);h+="; Path="+f.path}if(f.expires){if(!p(f.expires)||!Number.isFinite(f.expires.valueOf()))throw new TypeError(`option expires is invalid: ${f.expires}`);h+="; Expires="+f.expires.toUTCString()}if(f.httpOnly&&(h+="; HttpOnly"),f.secure&&(h+="; Secure"),f.partitioned&&(h+="; Partitioned"),f.priority)switch(typeof f.priority=="string"?f.priority.toLowerCase():void 0){case"low":h+="; Priority=Low";break;case"medium":h+="; Priority=Medium";break;case"high":h+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${f.priority}`)}if(f.sameSite)switch(typeof f.sameSite=="string"?f.sameSite.toLowerCase():f.sameSite){case!0:case"strict":h+="; SameSite=Strict";break;case"lax":h+="; SameSite=Lax";break;case"none":h+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${f.sameSite}`)}return h}function m(d){if(d.indexOf("%")===-1)return d;try{return decodeURIComponent(d)}catch{return d}}function p(d){return n.call(d)==="[object Date]"}return B}Fe();var ce="popstate";function Ae(e={}){function t(a,n){let{pathname:o,search:s,hash:c}=a.location;return X("",{pathname:o,search:s,hash:c},n.state&&n.state.usr||null,n.state&&n.state.key||"default")}function r(a,n){return typeof n=="string"?n:_(n)}return Me(t,r,null,e)}function E(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function S(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function De(){return Math.random().toString(36).substring(2,10)}function fe(e,t){return{usr:e.state,key:e.key,idx:t}}function X(e,t,r=null,a){return{pathname:typeof e=="string"?e:e.pathname,search:"",hash:"",...typeof t=="string"?A(t):t,state:r,key:t&&t.key||a||De()}}function _({pathname:e="/",search:t="",hash:r=""}){return t&&t!=="?"&&(e+=t.charAt(0)==="?"?t:"?"+t),r&&r!=="#"&&(e+=r.charAt(0)==="#"?r:"#"+r),e}function A(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substring(r),e=e.substring(0,r));let a=e.indexOf("?");a>=0&&(t.search=e.substring(a),e=e.substring(0,a)),e&&(t.pathname=e)}return t}function Me(e,t,r,a={}){let{window:n=document.defaultView,v5Compat:o=!1}=a,s=n.history,c="POP",l=null,i=m();i==null&&(i=0,s.replaceState({...s.state,idx:i},""));function m(){return(s.state||{idx:null}).idx}function p(){c="POP";let w=m(),h=w==null?null:w-i;i=w,l&&l({action:c,location:y.location,delta:h})}function d(w,h){c="PUSH";let g=X(y.location,w,h);i=m()+1;let x=fe(g,i),R=y.createHref(g);try{s.pushState(x,"",R)}catch(C){if(C instanceof DOMException&&C.name==="DataCloneError")throw C;n.location.assign(R)}o&&l&&l({action:c,location:y.location,delta:1})}function v(w,h){c="REPLACE";let g=X(y.location,w,h);i=m();let x=fe(g,i),R=y.createHref(g);s.replaceState(x,"",R),o&&l&&l({action:c,location:y.location,delta:0})}function f(w){return Be(w)}let y={get action(){return c},get location(){return e(n,s)},listen(w){if(l)throw new Error("A history only accepts one active listener");return n.addEventListener(ce,p),l=w,()=>{n.removeEventListener(ce,p),l=null}},createHref(w){return t(n,w)},createURL:f,encodeLocation(w){let h=f(w);return{pathname:h.pathname,search:h.search,hash:h.hash}},push:d,replace:v,go(w){return s.go(w)}};return y}function Be(e,t=!1){let r="http://localhost";typeof window<"u"&&(r=window.location.origin!=="null"?window.location.origin:window.location.href),E(r,"No window.location.(origin|href) available to create URL");let a=typeof e=="string"?e:_(e);return a=a.replace(/ $/,"%20"),!t&&a.startsWith("//")&&(a=r+a),new URL(a,r)}function pe(e,t,r="/"){return Ue(e,t,r,!1)}function Ue(e,t,r,a){let n=typeof t=="string"?A(t):t,o=$(n.pathname||"/",r);if(o==null)return null;let s=ye(e);_e(s);let c=null;for(let l=0;c==null&&l<s.length;++l){let i=Xe(o);c=qe(s[l],i,a)}return c}function ye(e,t=[],r=[],a=""){let n=(o,s,c)=>{let l={relativePath:c===void 0?o.path||"":c,caseSensitive:o.caseSensitive===!0,childrenIndex:s,route:o};l.relativePath.startsWith("/")&&(E(l.relativePath.startsWith(a),`Absolute route path "${l.relativePath}" nested under path "${a}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),l.relativePath=l.relativePath.slice(a.length));let i=k([a,l.relativePath]),m=r.concat(l);o.children&&o.children.length>0&&(E(o.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${i}".`),ye(o.children,t,m,i)),!(o.path==null&&!o.index)&&t.push({path:i,score:Ke(i,o.index),routesMeta:m})};return e.forEach((o,s)=>{var c;if(o.path===""||!((c=o.path)!=null&&c.includes("?")))n(o,s);else for(let l of ge(o.path))n(o,s,l)}),t}function ge(e){let t=e.split("/");if(t.length===0)return[];let[r,...a]=t,n=r.endsWith("?"),o=r.replace(/\?$/,"");if(a.length===0)return n?[o,""]:[o];let s=ge(a.join("/")),c=[];return c.push(...s.map(l=>l===""?o:[o,l].join("/"))),n&&c.push(...s),c.map(l=>e.startsWith("/")&&l===""?"/":l)}function _e(e){e.sort((t,r)=>t.score!==r.score?r.score-t.score:Ye(t.routesMeta.map(a=>a.childrenIndex),r.routesMeta.map(a=>a.childrenIndex)))}var He=/^:[\w-]+$/,We=3,je=2,ze=1,Ve=10,Je=-2,de=e=>e==="*";function Ke(e,t){let r=e.split("/"),a=r.length;return r.some(de)&&(a+=Je),t&&(a+=je),r.filter(n=>!de(n)).reduce((n,o)=>n+(He.test(o)?We:o===""?ze:Ve),a)}function Ye(e,t){return e.length===t.length&&e.slice(0,-1).every((a,n)=>a===t[n])?e[e.length-1]-t[t.length-1]:0}function qe(e,t,r=!1){let{routesMeta:a}=e,n={},o="/",s=[];for(let c=0;c<a.length;++c){let l=a[c],i=c===a.length-1,m=o==="/"?t:t.slice(o.length)||"/",p=J({path:l.relativePath,caseSensitive:l.caseSensitive,end:i},m),d=l.route;if(!p&&i&&r&&!a[a.length-1].route.index&&(p=J({path:l.relativePath,caseSensitive:l.caseSensitive,end:!1},m)),!p)return null;Object.assign(n,p.params),s.push({params:n,pathname:k([o,p.pathname]),pathnameBase:tt(k([o,p.pathnameBase])),route:d}),p.pathnameBase!=="/"&&(o=k([o,p.pathnameBase]))}return s}function J(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[r,a]=Ge(e.path,e.caseSensitive,e.end),n=t.match(r);if(!n)return null;let o=n[0],s=o.replace(/(.)\/+$/,"$1"),c=n.slice(1);return{params:a.reduce((i,{paramName:m,isOptional:p},d)=>{if(m==="*"){let f=c[d]||"";s=o.slice(0,o.length-f.length).replace(/(.)\/+$/,"$1")}const v=c[d];return p&&!v?i[m]=void 0:i[m]=(v||"").replace(/%2F/g,"/"),i},{}),pathname:o,pathnameBase:s,pattern:e}}function Ge(e,t=!1,r=!0){S(e==="*"||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let a=[],n="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(s,c,l)=>(a.push({paramName:c,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(a.push({paramName:"*"}),n+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?n+="\\/*$":e!==""&&e!=="/"&&(n+="(?:(?=\\/|$))"),[new RegExp(n,t?void 0:"i"),a]}function Xe(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return S(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function $(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,a=e.charAt(r);return a&&a!=="/"?null:e.slice(r)||"/"}function Qe(e,t="/"){let{pathname:r,search:a="",hash:n=""}=typeof e=="string"?A(e):e;return{pathname:r?r.startsWith("/")?r:Ze(r,t):t,search:rt(a),hash:nt(n)}}function Ze(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(n=>{n===".."?r.length>1&&r.pop():n!=="."&&r.push(n)}),r.length>1?r.join("/"):"/"}function q(e,t,r,a){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(a)}].  Please separate it out to the \`to.${r}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function et(e){return e.filter((t,r)=>r===0||t.route.path&&t.route.path.length>0)}function ee(e){let t=et(e);return t.map((r,a)=>a===t.length-1?r.pathname:r.pathnameBase)}function te(e,t,r,a=!1){let n;typeof e=="string"?n=A(e):(n={...e},E(!n.pathname||!n.pathname.includes("?"),q("?","pathname","search",n)),E(!n.pathname||!n.pathname.includes("#"),q("#","pathname","hash",n)),E(!n.search||!n.search.includes("#"),q("#","search","hash",n)));let o=e===""||n.pathname==="",s=o?"/":n.pathname,c;if(s==null)c=r;else{let p=t.length-1;if(!a&&s.startsWith("..")){let d=s.split("/");for(;d[0]==="..";)d.shift(),p-=1;n.pathname=d.join("/")}c=p>=0?t[p]:"/"}let l=Qe(n,c),i=s&&s!=="/"&&s.endsWith("/"),m=(o||s===".")&&r.endsWith("/");return!l.pathname.endsWith("/")&&(i||m)&&(l.pathname+="/"),l}var k=e=>e.join("/").replace(/\/\/+/g,"/"),tt=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),rt=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,nt=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function at(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}var ve=["POST","PUT","PATCH","DELETE"];new Set(ve);var ot=["GET",...ve];new Set(ot);var D=u.createContext(null);D.displayName="DataRouter";var K=u.createContext(null);K.displayName="DataRouterState";var we=u.createContext({isTransitioning:!1});we.displayName="ViewTransition";var it=u.createContext(new Map);it.displayName="Fetchers";var lt=u.createContext(null);lt.displayName="Await";var P=u.createContext(null);P.displayName="Navigation";var H=u.createContext(null);H.displayName="Location";var L=u.createContext({outlet:null,matches:[],isDataRoute:!1});L.displayName="Route";var re=u.createContext(null);re.displayName="RouteError";function ut(e,{relative:t}={}){E(M(),"useHref() may be used only in the context of a <Router> component.");let{basename:r,navigator:a}=u.useContext(P),{hash:n,pathname:o,search:s}=W(e,{relative:t}),c=o;return r!=="/"&&(c=o==="/"?r:k([r,o])),a.createHref({pathname:c,search:s,hash:n})}function M(){return u.useContext(H)!=null}function O(){return E(M(),"useLocation() may be used only in the context of a <Router> component."),u.useContext(H).location}var xe="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function Ee(e){u.useContext(P).static||u.useLayoutEffect(e)}function ne(){let{isDataRoute:e}=u.useContext(L);return e?Ct():st()}function st(){E(M(),"useNavigate() may be used only in the context of a <Router> component.");let e=u.useContext(D),{basename:t,navigator:r}=u.useContext(P),{matches:a}=u.useContext(L),{pathname:n}=O(),o=JSON.stringify(ee(a)),s=u.useRef(!1);return Ee(()=>{s.current=!0}),u.useCallback((l,i={})=>{if(S(s.current,xe),!s.current)return;if(typeof l=="number"){r.go(l);return}let m=te(l,JSON.parse(o),n,i.relative==="path");e==null&&t!=="/"&&(m.pathname=m.pathname==="/"?t:k([t,m.pathname])),(i.replace?r.replace:r.push)(m,i.state,i)},[t,r,o,n,e])}var ct=u.createContext(null);function ft(e){let t=u.useContext(L).outlet;return t&&u.createElement(ct.Provider,{value:e},t)}function W(e,{relative:t}={}){let{matches:r}=u.useContext(L),{pathname:a}=O(),n=JSON.stringify(ee(r));return u.useMemo(()=>te(e,JSON.parse(n),a,t==="path"),[e,n,a,t])}function dt(e,t){return Re(e,t)}function Re(e,t,r,a){var h;E(M(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:n}=u.useContext(P),{matches:o}=u.useContext(L),s=o[o.length-1],c=s?s.params:{},l=s?s.pathname:"/",i=s?s.pathnameBase:"/",m=s&&s.route;{let g=m&&m.path||"";Ce(l,!m||g.endsWith("*")||g.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${l}" (under <Route path="${g}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${g}"> to <Route path="${g==="/"?"*":`${g}/*`}">.`)}let p=O(),d;if(t){let g=typeof t=="string"?A(t):t;E(i==="/"||((h=g.pathname)==null?void 0:h.startsWith(i)),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${i}" but pathname "${g.pathname}" was given in the \`location\` prop.`),d=g}else d=p;let v=d.pathname||"/",f=v;if(i!=="/"){let g=i.replace(/^\//,"").split("/");f="/"+v.replace(/^\//,"").split("/").slice(g.length).join("/")}let y=pe(e,{pathname:f});S(m||y!=null,`No routes matched location "${d.pathname}${d.search}${d.hash}" `),S(y==null||y[y.length-1].route.element!==void 0||y[y.length-1].route.Component!==void 0||y[y.length-1].route.lazy!==void 0,`Matched leaf route at location "${d.pathname}${d.search}${d.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let w=gt(y&&y.map(g=>Object.assign({},g,{params:Object.assign({},c,g.params),pathname:k([i,n.encodeLocation?n.encodeLocation(g.pathname).pathname:g.pathname]),pathnameBase:g.pathnameBase==="/"?i:k([i,n.encodeLocation?n.encodeLocation(g.pathnameBase).pathname:g.pathnameBase])})),o,r,a);return t&&w?u.createElement(H.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...d},navigationType:"POP"}},w):w}function ht(){let e=Rt(),t=at(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,a="rgba(200,200,200, 0.5)",n={padding:"0.5rem",backgroundColor:a},o={padding:"2px 4px",backgroundColor:a},s=null;return console.error("Error handled by React Router default ErrorBoundary:",e),s=u.createElement(u.Fragment,null,u.createElement("p",null,"💿 Hey developer 👋"),u.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",u.createElement("code",{style:o},"ErrorBoundary")," or"," ",u.createElement("code",{style:o},"errorElement")," prop on your route.")),u.createElement(u.Fragment,null,u.createElement("h2",null,"Unexpected Application Error!"),u.createElement("h3",{style:{fontStyle:"italic"}},t),r?u.createElement("pre",{style:n},r):null,s)}var mt=u.createElement(ht,null),pt=class extends u.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||t.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return this.state.error!==void 0?u.createElement(L.Provider,{value:this.props.routeContext},u.createElement(re.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function yt({routeContext:e,match:t,children:r}){let a=u.useContext(D);return a&&a.static&&a.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=t.route.id),u.createElement(L.Provider,{value:e},r)}function gt(e,t=[],r=null,a=null){if(e==null){if(!r)return null;if(r.errors)e=r.matches;else if(t.length===0&&!r.initialized&&r.matches.length>0)e=r.matches;else return null}let n=e,o=r==null?void 0:r.errors;if(o!=null){let l=n.findIndex(i=>i.route.id&&(o==null?void 0:o[i.route.id])!==void 0);E(l>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(o).join(",")}`),n=n.slice(0,Math.min(n.length,l+1))}let s=!1,c=-1;if(r)for(let l=0;l<n.length;l++){let i=n[l];if((i.route.HydrateFallback||i.route.hydrateFallbackElement)&&(c=l),i.route.id){let{loaderData:m,errors:p}=r,d=i.route.loader&&!m.hasOwnProperty(i.route.id)&&(!p||p[i.route.id]===void 0);if(i.route.lazy||d){s=!0,c>=0?n=n.slice(0,c+1):n=[n[0]];break}}}return n.reduceRight((l,i,m)=>{let p,d=!1,v=null,f=null;r&&(p=o&&i.route.id?o[i.route.id]:void 0,v=i.route.errorElement||mt,s&&(c<0&&m===0?(Ce("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),d=!0,f=null):c===m&&(d=!0,f=i.route.hydrateFallbackElement||null)));let y=t.concat(n.slice(0,m+1)),w=()=>{let h;return p?h=v:d?h=f:i.route.Component?h=u.createElement(i.route.Component,null):i.route.element?h=i.route.element:h=l,u.createElement(yt,{match:i,routeContext:{outlet:l,matches:y,isDataRoute:r!=null},children:h})};return r&&(i.route.ErrorBoundary||i.route.errorElement||m===0)?u.createElement(pt,{location:r.location,revalidation:r.revalidation,component:v,error:p,children:w(),routeContext:{outlet:null,matches:y,isDataRoute:!0}}):w()},null)}function ae(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function vt(e){let t=u.useContext(D);return E(t,ae(e)),t}function wt(e){let t=u.useContext(K);return E(t,ae(e)),t}function xt(e){let t=u.useContext(L);return E(t,ae(e)),t}function oe(e){let t=xt(e),r=t.matches[t.matches.length-1];return E(r.route.id,`${e} can only be used on routes that contain a unique "id"`),r.route.id}function Et(){return oe("useRouteId")}function Rt(){var a;let e=u.useContext(re),t=wt("useRouteError"),r=oe("useRouteError");return e!==void 0?e:(a=t.errors)==null?void 0:a[r]}function Ct(){let{router:e}=vt("useNavigate"),t=oe("useNavigate"),r=u.useRef(!1);return Ee(()=>{r.current=!0}),u.useCallback(async(n,o={})=>{S(r.current,xe),r.current&&(typeof n=="number"?e.navigate(n):await e.navigate(n,{fromRouteId:t,...o}))},[e,t])}var he={};function Ce(e,t,r){!t&&!he[e]&&(he[e]=!0,S(!1,r))}u.memo(bt);function bt({routes:e,future:t,state:r}){return Re(e,void 0,r,t)}function ur({to:e,replace:t,state:r,relative:a}){E(M(),"<Navigate> may be used only in the context of a <Router> component.");let{static:n}=u.useContext(P);S(!n,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:o}=u.useContext(L),{pathname:s}=O(),c=ne(),l=te(e,ee(o),s,a==="path"),i=JSON.stringify(l);return u.useEffect(()=>{c(JSON.parse(i),{replace:t,state:r,relative:a})},[c,i,a,t,r]),null}function sr(e){return ft(e.context)}function St(e){E(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function Pt({basename:e="/",children:t=null,location:r,navigationType:a="POP",navigator:n,static:o=!1}){E(!M(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let s=e.replace(/^\/*/,"/"),c=u.useMemo(()=>({basename:s,navigator:n,static:o,future:{}}),[s,n,o]);typeof r=="string"&&(r=A(r));let{pathname:l="/",search:i="",hash:m="",state:p=null,key:d="default"}=r,v=u.useMemo(()=>{let f=$(l,s);return f==null?null:{location:{pathname:f,search:i,hash:m,state:p,key:d},navigationType:a}},[s,l,i,m,p,d,a]);return S(v!=null,`<Router basename="${s}"> is not able to match the URL "${l}${i}${m}" because it does not start with the basename, so the <Router> won't render anything.`),v==null?null:u.createElement(P.Provider,{value:c},u.createElement(H.Provider,{children:t,value:v}))}function cr({children:e,location:t}){return dt(Q(e),t)}function Q(e,t=[]){let r=[];return u.Children.forEach(e,(a,n)=>{if(!u.isValidElement(a))return;let o=[...t,n];if(a.type===u.Fragment){r.push.apply(r,Q(a.props.children,o));return}E(a.type===St,`[${typeof a.type=="string"?a.type:a.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),E(!a.props.index||!a.props.children,"An index route cannot have child routes.");let s={id:a.props.id||o.join("-"),caseSensitive:a.props.caseSensitive,element:a.props.element,Component:a.props.Component,index:a.props.index,path:a.props.path,loader:a.props.loader,action:a.props.action,hydrateFallbackElement:a.props.hydrateFallbackElement,HydrateFallback:a.props.HydrateFallback,errorElement:a.props.errorElement,ErrorBoundary:a.props.ErrorBoundary,hasErrorBoundary:a.props.hasErrorBoundary===!0||a.props.ErrorBoundary!=null||a.props.errorElement!=null,shouldRevalidate:a.props.shouldRevalidate,handle:a.props.handle,lazy:a.props.lazy};a.props.children&&(s.children=Q(a.props.children,o)),r.push(s)}),r}var z="get",V="application/x-www-form-urlencoded";function Y(e){return e!=null&&typeof e.tagName=="string"}function Lt(e){return Y(e)&&e.tagName.toLowerCase()==="button"}function kt(e){return Y(e)&&e.tagName.toLowerCase()==="form"}function $t(e){return Y(e)&&e.tagName.toLowerCase()==="input"}function Ot(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function It(e,t){return e.button===0&&(!t||t==="_self")&&!Ot(e)}function Z(e=""){return new URLSearchParams(typeof e=="string"||Array.isArray(e)||e instanceof URLSearchParams?e:Object.keys(e).reduce((t,r)=>{let a=e[r];return t.concat(Array.isArray(a)?a.map(n=>[r,n]):[[r,a]])},[]))}function Nt(e,t){let r=Z(e);return t&&t.forEach((a,n)=>{r.has(n)||t.getAll(n).forEach(o=>{r.append(n,o)})}),r}var j=null;function Tt(){if(j===null)try{new FormData(document.createElement("form"),0),j=!1}catch{j=!0}return j}var Ft=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function G(e){return e!=null&&!Ft.has(e)?(S(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${V}"`),null):e}function At(e,t){let r,a,n,o,s;if(kt(e)){let c=e.getAttribute("action");a=c?$(c,t):null,r=e.getAttribute("method")||z,n=G(e.getAttribute("enctype"))||V,o=new FormData(e)}else if(Lt(e)||$t(e)&&(e.type==="submit"||e.type==="image")){let c=e.form;if(c==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let l=e.getAttribute("formaction")||c.getAttribute("action");if(a=l?$(l,t):null,r=e.getAttribute("formmethod")||c.getAttribute("method")||z,n=G(e.getAttribute("formenctype"))||G(c.getAttribute("enctype"))||V,o=new FormData(c,e),!Tt()){let{name:i,type:m,value:p}=e;if(m==="image"){let d=i?`${i}.`:"";o.append(`${d}x`,"0"),o.append(`${d}y`,"0")}else i&&o.append(i,p)}}else{if(Y(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');r=z,a=null,n=V,s=e}return o&&n==="text/plain"&&(s=o,o=void 0),{action:a,method:r.toLowerCase(),encType:n,formData:o,body:s}}function ie(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}async function Dt(e,t){if(e.id in t)return t[e.id];try{let r=await import(e.module);return t[e.id]=r,r}catch(r){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(r),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function Mt(e){return e==null?!1:e.href==null?e.rel==="preload"&&typeof e.imageSrcSet=="string"&&typeof e.imageSizes=="string":typeof e.rel=="string"&&typeof e.href=="string"}async function Bt(e,t,r){let a=await Promise.all(e.map(async n=>{let o=t.routes[n.route.id];if(o){let s=await Dt(o,r);return s.links?s.links():[]}return[]}));return Wt(a.flat(1).filter(Mt).filter(n=>n.rel==="stylesheet"||n.rel==="preload").map(n=>n.rel==="stylesheet"?{...n,rel:"prefetch",as:"style"}:{...n,rel:"prefetch"}))}function me(e,t,r,a,n,o){let s=(l,i)=>r[i]?l.route.id!==r[i].route.id:!0,c=(l,i)=>{var m;return r[i].pathname!==l.pathname||((m=r[i].route.path)==null?void 0:m.endsWith("*"))&&r[i].params["*"]!==l.params["*"]};return o==="assets"?t.filter((l,i)=>s(l,i)||c(l,i)):o==="data"?t.filter((l,i)=>{var p;let m=a.routes[l.route.id];if(!m||!m.hasLoader)return!1;if(s(l,i)||c(l,i))return!0;if(l.route.shouldRevalidate){let d=l.route.shouldRevalidate({currentUrl:new URL(n.pathname+n.search+n.hash,window.origin),currentParams:((p=r[0])==null?void 0:p.params)||{},nextUrl:new URL(e,window.origin),nextParams:l.params,defaultShouldRevalidate:!0});if(typeof d=="boolean")return d}return!0}):[]}function Ut(e,t,{includeHydrateFallback:r}={}){return _t(e.map(a=>{let n=t.routes[a.route.id];if(!n)return[];let o=[n.module];return n.clientActionModule&&(o=o.concat(n.clientActionModule)),n.clientLoaderModule&&(o=o.concat(n.clientLoaderModule)),r&&n.hydrateFallbackModule&&(o=o.concat(n.hydrateFallbackModule)),n.imports&&(o=o.concat(n.imports)),o}).flat(1))}function _t(e){return[...new Set(e)]}function Ht(e){let t={},r=Object.keys(e).sort();for(let a of r)t[a]=e[a];return t}function Wt(e,t){let r=new Set;return new Set(t),e.reduce((a,n)=>{let o=JSON.stringify(Ht(n));return r.has(o)||(r.add(o),a.push({key:o,link:n})),a},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var jt=new Set([100,101,204,205]);function zt(e,t){let r=typeof e=="string"?new URL(e,typeof window>"u"?"server://singlefetch/":window.location.origin):e;return r.pathname==="/"?r.pathname="_root.data":t&&$(r.pathname,t)==="/"?r.pathname=`${t.replace(/\/$/,"")}/_root.data`:r.pathname=`${r.pathname.replace(/\/$/,"")}.data`,r}function be(){let e=u.useContext(D);return ie(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function Vt(){let e=u.useContext(K);return ie(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var le=u.createContext(void 0);le.displayName="FrameworkContext";function Se(){let e=u.useContext(le);return ie(e,"You must render this element inside a <HydratedRouter> element"),e}function Jt(e,t){let r=u.useContext(le),[a,n]=u.useState(!1),[o,s]=u.useState(!1),{onFocus:c,onBlur:l,onMouseEnter:i,onMouseLeave:m,onTouchStart:p}=t,d=u.useRef(null);u.useEffect(()=>{if(e==="render"&&s(!0),e==="viewport"){let y=h=>{h.forEach(g=>{s(g.isIntersecting)})},w=new IntersectionObserver(y,{threshold:.5});return d.current&&w.observe(d.current),()=>{w.disconnect()}}},[e]),u.useEffect(()=>{if(a){let y=setTimeout(()=>{s(!0)},100);return()=>{clearTimeout(y)}}},[a]);let v=()=>{n(!0)},f=()=>{n(!1),s(!1)};return r?e!=="intent"?[o,d,{}]:[o,d,{onFocus:U(c,v),onBlur:U(l,f),onMouseEnter:U(i,v),onMouseLeave:U(m,f),onTouchStart:U(p,v)}]:[!1,d,{}]}function U(e,t){return r=>{e&&e(r),r.defaultPrevented||t(r)}}function Kt({page:e,...t}){let{router:r}=be(),a=u.useMemo(()=>pe(r.routes,e,r.basename),[r.routes,e,r.basename]);return a?u.createElement(qt,{page:e,matches:a,...t}):null}function Yt(e){let{manifest:t,routeModules:r}=Se(),[a,n]=u.useState([]);return u.useEffect(()=>{let o=!1;return Bt(e,t,r).then(s=>{o||n(s)}),()=>{o=!0}},[e,t,r]),a}function qt({page:e,matches:t,...r}){let a=O(),{manifest:n,routeModules:o}=Se(),{basename:s}=be(),{loaderData:c,matches:l}=Vt(),i=u.useMemo(()=>me(e,t,l,n,a,"data"),[e,t,l,n,a]),m=u.useMemo(()=>me(e,t,l,n,a,"assets"),[e,t,l,n,a]),p=u.useMemo(()=>{if(e===a.pathname+a.search+a.hash)return[];let f=new Set,y=!1;if(t.forEach(h=>{var x;let g=n.routes[h.route.id];!g||!g.hasLoader||(!i.some(R=>R.route.id===h.route.id)&&h.route.id in c&&((x=o[h.route.id])!=null&&x.shouldRevalidate)||g.hasClientLoader?y=!0:f.add(h.route.id))}),f.size===0)return[];let w=zt(e,s);return y&&f.size>0&&w.searchParams.set("_routes",t.filter(h=>f.has(h.route.id)).map(h=>h.route.id).join(",")),[w.pathname+w.search]},[s,c,a,n,i,t,e,o]),d=u.useMemo(()=>Ut(m,n),[m,n]),v=Yt(m);return u.createElement(u.Fragment,null,p.map(f=>u.createElement("link",{key:f,rel:"prefetch",as:"fetch",href:f,...r})),d.map(f=>u.createElement("link",{key:f,rel:"modulepreload",href:f,...r})),v.map(({key:f,link:y})=>u.createElement("link",{key:f,...y})))}function Gt(...e){return t=>{e.forEach(r=>{typeof r=="function"?r(t):r!=null&&(r.current=t)})}}var Pe=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{Pe&&(window.__reactRouterVersion="7.6.2")}catch{}function fr({basename:e,children:t,window:r}){let a=u.useRef();a.current==null&&(a.current=Ae({window:r,v5Compat:!0}));let n=a.current,[o,s]=u.useState({action:n.action,location:n.location}),c=u.useCallback(l=>{u.startTransition(()=>s(l))},[s]);return u.useLayoutEffect(()=>n.listen(c),[n,c]),u.createElement(Pt,{basename:e,children:t,location:o.location,navigationType:o.action,navigator:n})}var Le=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,ke=u.forwardRef(function({onClick:t,discover:r="render",prefetch:a="none",relative:n,reloadDocument:o,replace:s,state:c,target:l,to:i,preventScrollReset:m,viewTransition:p,...d},v){let{basename:f}=u.useContext(P),y=typeof i=="string"&&Le.test(i),w,h=!1;if(typeof i=="string"&&y&&(w=i,Pe))try{let b=new URL(window.location.href),N=i.startsWith("//")?new URL(b.protocol+i):new URL(i),ue=$(N.pathname,f);N.origin===b.origin&&ue!=null?i=ue+N.search+N.hash:h=!0}catch{S(!1,`<Link to="${i}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let g=ut(i,{relative:n}),[x,R,C]=Jt(a,d),F=er(i,{replace:s,state:c,target:l,preventScrollReset:m,relative:n,viewTransition:p});function I(b){t&&t(b),b.defaultPrevented||F(b)}let T=u.createElement("a",{...d,...C,href:w||g,onClick:h||o?t:I,ref:Gt(v,R),target:l,"data-discover":!y&&r==="render"?"true":void 0});return x&&!y?u.createElement(u.Fragment,null,T,u.createElement(Kt,{page:g})):T});ke.displayName="Link";var Xt=u.forwardRef(function({"aria-current":t="page",caseSensitive:r=!1,className:a="",end:n=!1,style:o,to:s,viewTransition:c,children:l,...i},m){let p=W(s,{relative:i.relative}),d=O(),v=u.useContext(K),{navigator:f,basename:y}=u.useContext(P),w=v!=null&&or(p)&&c===!0,h=f.encodeLocation?f.encodeLocation(p).pathname:p.pathname,g=d.pathname,x=v&&v.navigation&&v.navigation.location?v.navigation.location.pathname:null;r||(g=g.toLowerCase(),x=x?x.toLowerCase():null,h=h.toLowerCase()),x&&y&&(x=$(x,y)||x);const R=h!=="/"&&h.endsWith("/")?h.length-1:h.length;let C=g===h||!n&&g.startsWith(h)&&g.charAt(R)==="/",F=x!=null&&(x===h||!n&&x.startsWith(h)&&x.charAt(h.length)==="/"),I={isActive:C,isPending:F,isTransitioning:w},T=C?t:void 0,b;typeof a=="function"?b=a(I):b=[a,C?"active":null,F?"pending":null,w?"transitioning":null].filter(Boolean).join(" ");let N=typeof o=="function"?o(I):o;return u.createElement(ke,{...i,"aria-current":T,className:b,ref:m,style:N,to:s,viewTransition:c},typeof l=="function"?l(I):l)});Xt.displayName="NavLink";var Qt=u.forwardRef(({discover:e="render",fetcherKey:t,navigate:r,reloadDocument:a,replace:n,state:o,method:s=z,action:c,onSubmit:l,relative:i,preventScrollReset:m,viewTransition:p,...d},v)=>{let f=nr(),y=ar(c,{relative:i}),w=s.toLowerCase()==="get"?"get":"post",h=typeof c=="string"&&Le.test(c),g=x=>{if(l&&l(x),x.defaultPrevented)return;x.preventDefault();let R=x.nativeEvent.submitter,C=(R==null?void 0:R.getAttribute("formmethod"))||s;f(R||x.currentTarget,{fetcherKey:t,method:C,navigate:r,replace:n,state:o,relative:i,preventScrollReset:m,viewTransition:p})};return u.createElement("form",{ref:v,method:w,action:y,onSubmit:a?l:g,...d,"data-discover":!h&&e==="render"?"true":void 0})});Qt.displayName="Form";function Zt(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function $e(e){let t=u.useContext(D);return E(t,Zt(e)),t}function er(e,{target:t,replace:r,state:a,preventScrollReset:n,relative:o,viewTransition:s}={}){let c=ne(),l=O(),i=W(e,{relative:o});return u.useCallback(m=>{if(It(m,t)){m.preventDefault();let p=r!==void 0?r:_(l)===_(i);c(e,{replace:p,state:a,preventScrollReset:n,relative:o,viewTransition:s})}},[l,c,i,r,a,t,e,n,o,s])}function dr(e){S(typeof URLSearchParams<"u","You cannot use the `useSearchParams` hook in a browser that does not support the URLSearchParams API. If you need to support Internet Explorer 11, we recommend you load a polyfill such as https://github.com/ungap/url-search-params.");let t=u.useRef(Z(e)),r=u.useRef(!1),a=O(),n=u.useMemo(()=>Nt(a.search,r.current?null:t.current),[a.search]),o=ne(),s=u.useCallback((c,l)=>{const i=Z(typeof c=="function"?c(n):c);r.current=!0,o("?"+i,l)},[o,n]);return[n,s]}var tr=0,rr=()=>`__${String(++tr)}__`;function nr(){let{router:e}=$e("useSubmit"),{basename:t}=u.useContext(P),r=Et();return u.useCallback(async(a,n={})=>{let{action:o,method:s,encType:c,formData:l,body:i}=At(a,t);if(n.navigate===!1){let m=n.fetcherKey||rr();await e.fetch(m,r,n.action||o,{preventScrollReset:n.preventScrollReset,formData:l,body:i,formMethod:n.method||s,formEncType:n.encType||c,flushSync:n.flushSync})}else await e.navigate(n.action||o,{preventScrollReset:n.preventScrollReset,formData:l,body:i,formMethod:n.method||s,formEncType:n.encType||c,replace:n.replace,state:n.state,fromRouteId:r,flushSync:n.flushSync,viewTransition:n.viewTransition})},[e,t,r])}function ar(e,{relative:t}={}){let{basename:r}=u.useContext(P),a=u.useContext(L);E(a,"useFormAction must be used inside a RouteContext");let[n]=a.matches.slice(-1),o={...W(e||".",{relative:t})},s=O();if(e==null){o.search=s.search;let c=new URLSearchParams(o.search),l=c.getAll("index");if(l.some(m=>m==="")){c.delete("index"),l.filter(p=>p).forEach(p=>c.append("index",p));let m=c.toString();o.search=m?`?${m}`:""}}return(!e||e===".")&&n.route.index&&(o.search=o.search?o.search.replace(/^\?/,"?index&"):"?index"),r!=="/"&&(o.pathname=o.pathname==="/"?r:k([r,o.pathname])),_(o)}function or(e,t={}){let r=u.useContext(we);E(r!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:a}=$e("useViewTransitionState"),n=W(e,{relative:t.relative});if(!r.isTransitioning)return!1;let o=$(r.currentLocation.pathname,a)||r.currentLocation.pathname,s=$(r.nextLocation.pathname,a)||r.nextLocation.pathname;return J(n.pathname,s)!=null||J(n.pathname,o)!=null}[...jt];export{fr as B,Xt as N,sr as O,Te as R,lr as a,O as b,ur as c,cr as d,St as e,dr as f,u as r,ne as u};
