import{a as C,j as e}from"./query-vendor-DbpRcGHB.js";import{u as L,a as V,b as U,E as M}from"./UsersPage-F_CFWuRK.js";import{C as T}from"./etws-api-BUsVSvPp.js";import{r as S,_ as O,$ as k,a0 as A,M as a,N as c,S as g,Q as y,U as F,V as b,W as N,B as w}from"./ui-components-B3WDh88j.js";import{u as I}from"./i18n-vendor-CzM1WOJE.js";import{d as B}from"./ui-vendor-BwzdWZX0.js";import{d as R}from"./form-vendor-mwNakpFF.js";import"./router-vendor-CX6yTN5-.js";import"./react-vendor-DJG_os-6.js";import"./index-CvPR-Eda.js";import"./api-client-xsH4HHeE.js";const q=new T(void 0,"/et-ws",void 0),P=()=>C({queryKey:["cities","select-list"],queryFn:async()=>(await q.apiCitiesSelectListGet()).data});function Q({message:r,size:s="md",className:m}){const{t:n}=I(),o=r||n("common.loading"),i={sm:"p-4",md:"p-8",lg:"p-12"},d={sm:"h-4 w-4",md:"h-5 w-5",lg:"h-6 w-6"};return e.jsx("div",{className:S("flex items-center justify-center",i[s],m),children:e.jsxs("div",{className:"flex items-center gap-3 text-muted-foreground",children:[e.jsx(B,{className:S("animate-spin",d[s])}),e.jsx("span",{className:"text-sm",children:o})]})})}const z=({userId:r,user:s,citiesData:m,schenkersData:n,onClose:o})=>{var E,v;const{t:i}=I(),d=L(),{register:l,handleSubmit:f,setValue:u,watch:x,formState:{isSubmitting:p}}=R({defaultValues:{id:r,eln:(s==null?void 0:s.eln)||"",firstName:(s==null?void 0:s.firstName)||"",surname:(s==null?void 0:s.surname)||"",familyName:(s==null?void 0:s.familyName)||"",op:(s==null?void 0:s.op)||"",cityId:(s==null?void 0:s.cityId)||0,region:(s==null?void 0:s.region)||"",clientNumber:(s==null?void 0:s.clientNumber)||"",schenkerId:(s==null?void 0:s.schenkerId)||0,userType:s!=null&&s.isMolOfOp?"MOL":"Installer",phoneNumber:(s==null?void 0:s.phoneNumber)||""}}),j=async t=>{await d.mutateAsync(t,{onSuccess:()=>{o()}})};return e.jsxs(e.Fragment,{children:[e.jsxs(O,{children:[e.jsx(k,{children:i("userEditForm.title")}),e.jsx(A,{children:i("userEditForm.description")})]}),e.jsxs("form",{onSubmit:f(j),className:"space-y-4 p-4 sm:p-6 overflow-y-auto",children:[e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(a,{htmlFor:"eln",className:"text-sm font-medium",children:i("userEditForm.fields.eln")}),e.jsx(c,{id:"eln",...l("eln"),disabled:!0,className:"w-full"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(a,{htmlFor:"firstName",className:"text-sm font-medium",children:i("userEditForm.fields.firstName")}),e.jsx(c,{id:"firstName",disabled:!0,...l("firstName"),className:"w-full"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(a,{htmlFor:"surname",className:"text-sm font-medium",children:i("userEditForm.fields.surname")}),e.jsx(c,{id:"surname",disabled:!0,...l("surname"),className:"w-full"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(a,{htmlFor:"familyName",className:"text-sm font-medium",children:i("userEditForm.fields.familyName")}),e.jsx(c,{id:"familyName",disabled:!0,...l("familyName"),className:"w-full"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(a,{htmlFor:"op",className:"text-sm font-medium",children:i("userEditForm.fields.op")}),e.jsxs(g,{value:((E=x("schenkerId"))==null?void 0:E.toString())||"",onValueChange:t=>{const h=t?parseInt(t,10):0;u("schenkerId",h)},children:[e.jsx(y,{className:"w-full",children:e.jsx(F,{placeholder:i("userEditForm.placeholders.selectOp")})}),e.jsx(b,{children:n==null?void 0:n.map(t=>e.jsx(N,{value:t.id||"",children:t.opCode},t.id))})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(a,{htmlFor:"city",className:"text-sm font-medium",children:i("userEditForm.fields.city")}),e.jsxs(g,{value:((v=x("cityId"))==null?void 0:v.toString())||"",onValueChange:t=>{const h=t?parseInt(t,10):0;u("cityId",h)},children:[e.jsx(y,{className:"w-full",children:e.jsx(F,{placeholder:i("userEditForm.placeholders.selectCity")})}),e.jsx(b,{children:m==null?void 0:m.map(t=>{var h;return e.jsx(N,{value:((h=t.id)==null?void 0:h.toString())||"",children:t.name},t.id)})})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(a,{htmlFor:"region",className:"text-sm font-medium",children:i("userEditForm.fields.region")}),e.jsx(c,{id:"region",disabled:!0,...l("region"),className:"w-full"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(a,{htmlFor:"status",className:"text-sm font-medium",children:i("userEditForm.fields.molStatus")}),e.jsxs(g,{value:x("userType")||"",onValueChange:t=>u("userType",t),children:[e.jsx(y,{className:"w-full",children:e.jsx(F,{placeholder:i("userEditForm.placeholders.selectStatus")})}),e.jsxs(b,{children:[e.jsx(N,{value:"Installer",children:i("userEditForm.options.no")}),e.jsx(N,{value:"MOL",children:i("userEditForm.options.yes")})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(a,{htmlFor:"clientNumber",className:"text-sm font-medium",children:i("userEditForm.fields.clientNumber")}),e.jsx(c,{id:"clientNumber",...l("clientNumber"),className:"w-full"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(a,{htmlFor:"phoneNumber",className:"text-sm font-medium",children:i("userEditForm.fields.phoneNumber")}),e.jsx(c,{id:"phoneNumber",...l("phoneNumber"),className:"w-full"})]})]})," ",e.jsxs("div",{className:"flex flex-col sm:flex-row justify-end gap-3 pt-4 mt-6 border-t",children:[e.jsx(w,{type:"button",variant:"outline",onClick:o,className:"w-full sm:w-auto order-2 sm:order-1",children:i("userEditForm.buttons.cancel")}),e.jsx(w,{type:"submit",disabled:p||d.isPending,className:"w-full sm:w-auto order-1 sm:order-2",children:p||d.isPending?i("userEditForm.buttons.saving"):i("userEditForm.buttons.save")})]})]})]})};function ee({userId:r,onClose:s}){const{data:m,isLoading:n,refetch:o}=P(),{data:i,isLoading:d,refetch:l}=V(),{data:f,isLoading:u,isError:x,refetch:p}=U(r),j=()=>{p(),o(),l()};return u||n||d?e.jsx(Q,{}):x||!f||!m||!i?e.jsx(M,{onRetry:j}):e.jsx(z,{userId:r,user:f,citiesData:m,schenkersData:i,onClose:s})}export{ee as UserEditDialog};
