import { useTranslation } from "react-i18next";
import DropdownTestPage from "./DropdownTestPage";

export default function HomePage() {
  const { t } = useTranslation();
  return (
    <div className="flex items-center justify-center flex-col mt-64">
      <DropdownTestPage/>
          <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold group flex items-center justify-center text-center">
        <span>
          {t("welcome")}
        </span>
        <span className="text-blue-500 group-hover:text-blue-700 transition-colors duration-300">
          {t("ET")}
        </span>
      </h1>

      <div className="flex items-center justify-center mt-5">
        <img
          className="w-full max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg h-auto"
          src="/et-removebg-preview.png"
          alt="ET"
        />
      </div>
    </div>
  );
}
