﻿namespace EtDb.DataHandlers
{
    using EtDb.ApiClients.EtDb.Context;
    using EtDb.ApiClients.EtDb.Enums;
    using EtDb.ApiClients.EtDb.Models;
    using EtDb.DataHandlers.Enums;
    using EtDb.DataHandlers.Interfaces;
    using EtDb.DataHandlers.Models;
    using Microsoft.EntityFrameworkCore;

    public class HistoryDataHandler : BaseDataHandler, IHistoryDataHandler
    {
        private const string SourceSystemBatch = "Batch";
        private const string SourceSystemET = "System Е.Т.";

        public HistoryDataHandler(Lazy<EtDbContext> dbContext)
            : base(dbContext)
        {
        }

        public IQueryable<History> GetAll()
        {
            return this.dbContext.Value.Histories;
        }

        public IQueryable<History> GetHistoryByItemId(int itemId)
        {
            return this.GetAll().Where(h => h.ItemId == itemId);
        }

        public IQueryable<History> GetHistoryByFromUserId(string userId)
        {
            return this.GetAll().Where(h => h.FromUserId == userId);
        }

        public IQueryable<History> GetHistoryByToUserId(string userId)
        {
            return this.GetAll().Where(h => h.ToUserId == userId);
        }

        public IQueryable<History> GetHistoryResrvedItemsByFromUserId(string userId)
        {
            return this.GetHistoryByFromUserId(userId)
                .Include(h => h.Item)
                .Where(h => h.DocStatus == (int)DocStatus.Reserved);
        }

        public IQueryable<History> GetHistoryItemsToAccept(string userId)
        {
            return this.GetHistoryByToUserId(userId)
                       .Include(h => h.Item.EquipmentType)
                       .Include(h => h.FromUser)
                       .Where(h => h.DocStatus == (int)DocStatus.WaitingForConfirmation);
        }

        public IQueryable<History> GetHistoryTransferedItems(string userId)
        {
            return this.GetHistoryByFromUserId(userId)
                .Include(h => h.Item.EquipmentType)
                       .Include(h => h.ToUser)
                       .Where(h => h.DocStatus == (int)DocStatus.WaitingForConfirmation);
        }

        public IQueryable<History> GetHistoryFromUserByItemId(string userId, int itemId)
        {
            return this.GetAll()
                       .Where(h => h.FromUserId == userId && h.ItemId == itemId);
        }

        public IQueryable<History> GetHistoryToUserByItemId(string userId, int itemId)
        {
            return this.GetAll()
                       .Where(h => h.ToUserId == userId && h.ItemId == itemId);
        }

        public IQueryable<string> GetHistoryToUserIdUnacceptedItems(double days)
        {
            var allHistoryEntries = this.GetAll();
            return this.GetHistoryToUserIdUnacceptedItemsFromCollection(allHistoryEntries, days);
        }

        public IQueryable<string> GetHistoryToUserIdUnacceptedItems(IQueryable<History> historyEntries, double days)
        {
            return this.GetHistoryToUserIdUnacceptedItemsFromCollection(historyEntries, days);
        }

        public async Task<IDictionary<string, List<string>>> GetHistoryToUserIdCanceledItemsForCurrentDayAsync()
        {
            DateTime currentDay = DateTime.Now.AddDays(-1);

            var result = await this.GetAll()
                .Where(h => h.OperationType != (int)OperationType.AddFromSAP || h.OperationType != (int)OperationType.AddFromProvisioning)
                .Where(h => h.DocStatus == (int)DocStatus.CancelledBySystem || h.DocStatus == (int)DocStatus.CancelledByS2S)
                .Where(h => h.ToUserId != null)
                .Where(h => h.StatusHistory.Any(s =>
                    (s.DocStatusNew == (int)DocStatus.CancelledByS2S || s.DocStatusNew == (int)DocStatus.CancelledBySystem) &&
                    s.DateInsert >= currentDay))
                .Include(h => h.Item)
                .ToListAsync();

            return result
                .GroupBy(h => h.ToUserId)
                .ToDictionary(
                    g => g.Key,
                    g => g.Select(h => h.Item.EquipmentSerialNum)
                          .Distinct()
                          .ToList()
                );
        }

        public void UpdateHistoryDelieverItems(
            History historyEntry,
            DocStatus docStatus,
            string toUserId,
            string currentDeliveryNum,
            int? postOfficeId = null,
            string waybillNum = null,
            DateTime? waybillDate = null)
        {
            historyEntry.DocStatus = (int)docStatus;
            historyEntry.ToUserId = toUserId;
            historyEntry.IntDeliveryNum = currentDeliveryNum;
            historyEntry.PostOfficeId = postOfficeId;
            historyEntry.WaybillNum = waybillNum;
            historyEntry.WaybillDate = waybillDate;

            this.dbContext.Value.Histories.Update(historyEntry);
        }

        public void UpdateHistoryAcceptItems(History historyEntry, DocStatus docStatus, RefuseReasonsList? refuseReason)
        {
            historyEntry.DocStatus = (int)docStatus;
            historyEntry.RefuseReason = (int?)refuseReason;

            this.dbContext.Value.Histories.Update(historyEntry);
        }

        public async Task UpdateHistoryTransfersToBlockedUsersAsync(IEnumerable<string> users)
        {
            foreach (var userId in users)
            {
                var historyEntries = await this.GetHistoryItemsToAccept(userId)
                    .Where(h => h.ToUser.Eln != "99999")
                    .ToListAsync();

                if (historyEntries != null)
                {
                    foreach (var entry in historyEntries)
                    {
                        await this.CreateStatusHistoryEntryAsync(entry.Id, null, (DocStatus)entry.DocStatus, DocStatus.CancelledBySystem, SourceSystemET);

                        entry.DocStatus = (int)DocStatus.CancelledBySystem;
                        entry.SourceSystem = SourceSystemET;
                        this.dbContext.Value.Histories.Update(entry);
                    }
                }
            }

            await this.dbContext.Value.SaveChangesAsync();
        }

        public async Task<KeyValuePair<string, IDictionary<string, TransferedItemsModel>>> CancelHistoryExpiredTransfersAsync(double days)
        {
            string message = null;
            IDictionary<string, TransferedItemsModel> usersPendingTransferedItems = new Dictionary<string, TransferedItemsModel>();

            DateTime cancellationDate = DateTime.Now.AddDays(-days);
            IList<History> historyEntries = await this.dbContext.Value.Histories
                .Include(h => h.FromUser)
                .Include(h => h.ToUser)
                .Include(h => h.Item)
                .Where(h => (h.DocStatus == (int)DocStatus.Reserved || h.DocStatus == (int)DocStatus.WaitingForConfirmation)
                            && h.InsertDate <= cancellationDate
                            && h.FromUser.Eln != "99999")
                .ToListAsync();

            int numberOfEntries = historyEntries.Count;
            if (numberOfEntries > 0)
            {
                foreach (var historyEntry in historyEntries)
                {
                    await this.CreateStatusHistoryEntryAsync(historyEntry.Id, null, (DocStatus)historyEntry.DocStatus, DocStatus.CancelledBySystem, SourceSystemBatch);

                    historyEntry.DocStatus = (int)DocStatus.CancelledBySystem;
                    historyEntry.SourceSystem = SourceSystemBatch;

                    this.dbContext.Value.Histories.Update(historyEntry);

                    string toUserId = historyEntry.ToUserId;
                    string fromUserId = historyEntry.FromUserId;
                    string itemSerialNumber = historyEntry.Item.EquipmentSerialNum;

                    if (toUserId != null)
                    {
                        if (!usersPendingTransferedItems.ContainsKey(toUserId))
                        {
                            usersPendingTransferedItems[toUserId] = new TransferedItemsModel(historyEntry.ToUser, fromUserId, itemSerialNumber);
                        }
                        else
                        {
                            usersPendingTransferedItems[toUserId].SerialNumbers.Add(itemSerialNumber);
                        }
                    }
                }

                await this.dbContext.Value.SaveChangesAsync();

                usersPendingTransferedItems = await this.GetUsersPendingTransferedItemsAsync(usersPendingTransferedItems);

                message = $"Cancelled transfers: {numberOfEntries}";
            }

            return new KeyValuePair<string, IDictionary<string, TransferedItemsModel>>(message, usersPendingTransferedItems);
        }

        public async Task<StatusHistory> CreateStatusHistoryEntryAsync(int historyEntryId, string fromUserId, DocStatus docStatusOld, DocStatus docStatusNew, string sourceSystem)
        {
            StatusHistory statusHistoryEntry = StatusHistory.CreateEntry(historyEntryId, fromUserId, docStatusOld, docStatusNew, sourceSystem);
            await this.dbContext.Value.StatusHistories.AddAsync(statusHistoryEntry);

            return statusHistoryEntry;
        }

        public async Task RemoveHistoryEntryAsync(History historyEntry)
        {
            this.dbContext.Value.Histories.Remove(historyEntry);
            await this.dbContext.Value.SaveChangesAsync();
        }

        public async Task RemoveHistoryEntriesAsync(IEnumerable<History> historyEntries)
        {
            this.dbContext.Value.ChangeTracker.AutoDetectChangesEnabled = false;

            foreach (var history in historyEntries)
            {
                this.dbContext.Value.Histories.Remove(history);
            }

            this.dbContext.Value.ChangeTracker.AutoDetectChangesEnabled = true;
            await this.dbContext.Value.SaveChangesAsync();
        }

        public void UpdateAdditionDate(History historyEntry)
        {
            if (historyEntry.Item.EquipmentType.SerialNumberRequired == 1)
            {
                historyEntry.Item.AdditionDate = DateTime.Now;
                this.dbContext.Value.Histories.Update(historyEntry);
            }
        }

        public IQueryable<StatusHistory> GetStatusHistory()
        {
            IQueryable<StatusHistory> statusHistory = this.dbContext.Value.StatusHistories;

            return statusHistory;
        }

        public async Task<IDictionary<string, TransferedItemsModel>> GetUsersPendingTransferedItemsAsync(IDictionary<string, TransferedItemsModel> usersCollection)
        {
            foreach (var userId in usersCollection.Keys.ToList())
            {
                var userItemsToAccept = this.GetHistoryItemsToAccept(userId);
                usersCollection[userId].TotalCount = await userItemsToAccept.CountAsync();

                var userUnacceptedItems = await this.GetHistoryToUserIdUnacceptedItems(userItemsToAccept, 7).ToListAsync();
                usersCollection[userId].ExpiredCount = userUnacceptedItems.Count;
            }

            return usersCollection;
        }

        public async Task<int> GetIncomingItemstCountAsync(int opId, DateTime fromDate, DateTime toDate)
        {
            var histories = this.dbContext.Value.Histories
                .Include(h => h.FromUser)
                .Include(h => h.ToUser)
                .Where(h => h.FromUser.Opcode == "OPVW")
                .Where(h => h.ItemValidity == "Y");

            if (opId != 0)
            {
                histories = histories.Where(h => h.ToUser.SchenkerId == opId);
            }

            var incomingEquipmentCount = await histories.CountAsync(h => h.InsertDate >= fromDate && h.InsertDate <= toDate);

            return incomingEquipmentCount;
        }

        public async Task<int> GetReturnedItemsFromClientCountAsync(int opId, DateTime fromDate, DateTime toDate)
        {
            var histories = this.dbContext.Value.Histories
                .Include(h => h.ToUser)
                .Where(h => h.SourceSystem == "SGW" && h.ItemValidity == "N");

            if (opId != 0)
            {
                histories = histories.Where(h => h.ToUser.SchenkerId == opId);
            }

            var returnedEquipmentFromClientCount = await histories.CountAsync(h => h.InsertDate >= fromDate && h.InsertDate <= toDate);

            return returnedEquipmentFromClientCount;
        }

        private IQueryable<string> GetHistoryToUserIdUnacceptedItemsFromCollection(IQueryable<History> historyEntries, double days)
        {
            DateTime warningDays = DateTime.Now.AddDays(-days);

            return historyEntries
                .Where(h => h.DocStatus == (int)DocStatus.WaitingForConfirmation &&
                            h.InsertDate.HasValue &&
                            h.InsertDate.Value < warningDays)
                .Select(h => h.ToUserId)
                .Distinct();
        }
    }
}
