import { lazy, Suspense } from "react";
import Navbar from "./components/Navbar";
import RequireAuth from "./components/auth/RequireAuth";
import { Route, Routes } from "react-router";
import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from "react-hot-toast";

// Lazy load components for better code splitting
const HomePage = lazy(() => import("./pages/HomePage"));
const Login = lazy(() => import("./pages/Login"));
const Correction = lazy(() => import("./components/correction/Correction"));
const UsersPage = lazy(() => import("./pages/administration/UsersPage"));

// Loading component
const LoadingSpinner = () => (
  <div className="flex items-center justify-center min-h-[200px]">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
  </div>
);

function App() {
  return (
    <ThemeProvider defaultTheme="dark" storageKey="vite-ui-theme">
      <Toaster position="top-right" reverseOrder={false} />
      <Navbar />
      <Suspense fallback={<LoadingSpinner />}>
        <Routes>
          <Route path="/login" element={<Login />} />
          <Route element={<RequireAuth />}>
            <Route path="/" element={<HomePage />} />
            <Route path="/transfer-correction" element={<Correction />} />
            <Route path="/administration/users" element={<UsersPage />} />
          </Route>
        </Routes>
      </Suspense>
    </ThemeProvider>
  );
}

export default App;
