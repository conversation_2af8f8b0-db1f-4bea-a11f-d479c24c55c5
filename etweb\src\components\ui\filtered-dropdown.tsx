import * as React from "react"
import { Check, ChevronDown, Search, X } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { Badge } from "@/components/ui/badge"

export interface DropdownOption {
  value: string
  label: string
  group?: string
  disabled?: boolean
  [key: string]: any // Allow additional properties
}

export interface FilteredDropdownProps {
  // Basic props
  options?: DropdownOption[]
  value?: string | string[]
  onValueChange?: (value: string | string[]) => void
  placeholder?: string
  emptyText?: string
  searchPlaceholder?: string
  
  // Multi-select
  multiple?: boolean
  maxSelected?: number
  
  // Filtering
  filterType?: 'internal' | 'api'
  onSearch?: (query: string) => Promise<DropdownOption[]> | DropdownOption[]
  searchDebounceMs?: number
  minSearchLength?: number
  
  // Grouping
  groupBy?: string
  groupLabels?: Record<string, string>
  
  // Styling
  triggerClassName?: string
  contentClassName?: string
  disabled?: boolean
  
  // Display
  displayValue?: (option: DropdownOption) => string
  renderOption?: (option: DropdownOption) => React.ReactNode
  showSearch?: boolean
  clearable?: boolean
  
  // Loading states
  loading?: boolean
  loadingText?: string
}

export function FilteredDropdown({
  options = [],
  value,
  onValueChange,
  placeholder = "Select option...",
  emptyText = "No options found",
  searchPlaceholder = "Search...",
  multiple = false,
  maxSelected,
  filterType = 'internal',
  onSearch,
  searchDebounceMs = 300,
  minSearchLength = 0,
  groupBy,
  groupLabels = {},
  triggerClassName,
  contentClassName,
  disabled = false,
  displayValue,
  renderOption,
  showSearch = true,
  clearable = false,
  loading = false,
  loadingText = "Loading...",
}: FilteredDropdownProps) {
  const [open, setOpen] = React.useState(false)
  const [searchQuery, setSearchQuery] = React.useState("")
  const [filteredOptions, setFilteredOptions] = React.useState<DropdownOption[]>(options)
  const [isSearching, setIsSearching] = React.useState(false)
  const searchTimeoutRef = React.useRef<NodeJS.Timeout>()

  // Convert value to array for easier handling
  const selectedValues = React.useMemo(() => {
    if (!value) return []
    return Array.isArray(value) ? value : [value]
  }, [value])

  // Get selected options
  const selectedOptions = React.useMemo(() => {
    return filteredOptions.filter(option => selectedValues.includes(option.value))
  }, [filteredOptions, selectedValues])

  // Handle search with debouncing
  const handleSearch = React.useCallback(async (query: string) => {
    setSearchQuery(query)
    
    if (filterType === 'internal') {
      // Internal filtering
      if (!query.trim()) {
        setFilteredOptions(options)
      } else {
        const filtered = options.filter(option =>
          option.label.toLowerCase().includes(query.toLowerCase()) ||
          option.value.toLowerCase().includes(query.toLowerCase())
        )
        setFilteredOptions(filtered)
      }
    } else if (filterType === 'api' && onSearch) {
      // API-based filtering
      if (query.length < minSearchLength) {
        setFilteredOptions([])
        return
      }

      setIsSearching(true)
      try {
        const results = await onSearch(query)
        setFilteredOptions(Array.isArray(results) ? results : [])
      } catch (error) {
        console.error('Search error:', error)
        setFilteredOptions([])
      } finally {
        setIsSearching(false)
      }
    }
  }, [filterType, options, onSearch, minSearchLength])

  // Debounced search effect
  React.useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current)
    }

    searchTimeoutRef.current = setTimeout(() => {
      handleSearch(searchQuery)
    }, searchDebounceMs)

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current)
      }
    }
  }, [searchQuery, handleSearch, searchDebounceMs])

  // Initialize filtered options
  React.useEffect(() => {
    if (filterType === 'internal') {
      setFilteredOptions(options)
    }
  }, [options, filterType])

  // Handle option selection
  const handleSelect = (optionValue: string) => {
    if (multiple) {
      const newValues = selectedValues.includes(optionValue)
        ? selectedValues.filter(v => v !== optionValue)
        : maxSelected && selectedValues.length >= maxSelected
        ? selectedValues
        : [...selectedValues, optionValue]
      
      onValueChange?.(newValues)
    } else {
      onValueChange?.(optionValue)
      setOpen(false)
    }
  }

  // Handle clear
  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation()
    onValueChange?.(multiple ? [] : "")
  }

  // Group options
  const groupedOptions = React.useMemo(() => {
    if (!groupBy) {
      return { '': filteredOptions }
    }

    return filteredOptions.reduce((groups, option) => {
      const group = option[groupBy] || ''
      if (!groups[group]) {
        groups[group] = []
      }
      groups[group].push(option)
      return groups
    }, {} as Record<string, DropdownOption[]>)
  }, [filteredOptions, groupBy])

  // Display text for trigger
  const displayText = React.useMemo(() => {
    if (selectedOptions.length === 0) return placeholder
    
    if (multiple) {
      if (selectedOptions.length === 1) {
        return displayValue ? displayValue(selectedOptions[0]) : selectedOptions[0].label
      }
      return `${selectedOptions.length} selected`
    }
    
    return displayValue ? displayValue(selectedOptions[0]) : selectedOptions[0].label
  }, [selectedOptions, placeholder, multiple, displayValue])

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-full justify-between font-normal",
            !selectedValues.length && "text-muted-foreground",
            triggerClassName
          )}
          disabled={disabled}
        >
          <div className="flex items-center gap-2 flex-1 min-w-0">
            <span className="truncate">{displayText}</span>
            {multiple && selectedOptions.length > 1 && (
              <Badge variant="secondary" className="ml-auto">
                {selectedOptions.length}
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-1 ml-2">
            {clearable && selectedValues.length > 0 && (
              <X
                className="h-4 w-4 opacity-50 hover:opacity-100"
                onClick={handleClear}
              />
            )}
            <ChevronDown className="h-4 w-4 opacity-50" />
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent 
        className={cn("w-[--radix-popover-trigger-width] p-0", contentClassName)}
        align="start"
      >
        <Command>
          {showSearch && (
            <div className="flex items-center border-b px-3">
              <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
              <input
                placeholder={searchPlaceholder}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
              />
            </div>
          )}
          <CommandList>
            {loading || isSearching ? (
              <div className="py-6 text-center text-sm text-muted-foreground">
                {loadingText}
              </div>
            ) : Object.keys(groupedOptions).length === 0 || 
                Object.values(groupedOptions).every(group => group.length === 0) ? (
              <CommandEmpty>{emptyText}</CommandEmpty>
            ) : (
              Object.entries(groupedOptions).map(([groupName, groupOptions]) => (
                <CommandGroup 
                  key={groupName} 
                  heading={groupName ? (groupLabels[groupName] || groupName) : undefined}
                >
                  {groupOptions.map((option) => (
                    <CommandItem
                      key={option.value}
                      value={option.value}
                      onSelect={() => handleSelect(option.value)}
                      disabled={option.disabled}
                      className="flex items-center justify-between"
                    >
                      <div className="flex items-center gap-2 flex-1 min-w-0">
                        {renderOption ? renderOption(option) : (
                          <span className="truncate">{option.label}</span>
                        )}
                      </div>
                      {selectedValues.includes(option.value) && (
                        <Check className="h-4 w-4 text-primary" />
                      )}
                    </CommandItem>
                  ))}
                </CommandGroup>
              ))
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
