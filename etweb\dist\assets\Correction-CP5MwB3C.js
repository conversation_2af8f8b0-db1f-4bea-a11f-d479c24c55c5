const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/Technician-C95efZXi.js","assets/query-vendor-DbpRcGHB.js","assets/router-vendor-CX6yTN5-.js","assets/react-vendor-DJG_os-6.js","assets/form-vendor-mwNakpFF.js","assets/index-CvPR-Eda.js","assets/ui-components-B3WDh88j.js","assets/ui-vendor-BwzdWZX0.js","assets/i18n-vendor-CzM1WOJE.js","assets/api-client-xsH4HHeE.js","assets/etws-api-BUsVSvPp.js","assets/index-DQLga5a_.css","assets/filtered-dropdown-BBzS_ulI.js","assets/badge-DNy6GwuQ.js","assets/ServiceId-BO7rki53.js","assets/Op-7awmWtOv.js","assets/Remove-Ct6VhGhL.js"])))=>i.map(i=>d[i]);
import{u as W,a as k,b as F,_ as I}from"./index-CvPR-Eda.js";import{j as e}from"./query-vendor-DbpRcGHB.js";import{o as V,s as z,a as L,u as A,c as M,b as j,d as $,F as B}from"./form-vendor-mwNakpFF.js";import{r as x}from"./router-vendor-CX6yTN5-.js";import{M as D,N as U,S as G,Q,U as X,V as Z,W as H,r as O,G as J,B as K}from"./ui-components-B3WDh88j.js";import{u as y}from"./i18n-vendor-CzM1WOJE.js";import{d as Y,e as C,f as w,g as ee,I as P}from"./ui-vendor-BwzdWZX0.js";import"./react-vendor-DJG_os-6.js";import"./api-client-xsH4HHeE.js";import"./etws-api-BUsVSvPp.js";function re(a,t,r){return r===void 0&&(r={}),function(d,f,c){try{return Promise.resolve(function(m,n){try{var u=(t!=null&&t.context,Promise.resolve(a[r.mode==="sync"?"validateSync":"validate"](d,Object.assign({abortEarly:!1},t,{context:f}))).then(function(i){return c.shouldUseNativeValidation&&V({},c),{values:r.raw?Object.assign({},d):i,errors:{}}}))}catch(i){return n(i)}return u&&u.then?u.then(void 0,n):u}(0,function(m){if(!m.inner)throw m;return{values:{},errors:z((n=m,u=!c.shouldUseNativeValidation&&c.criteriaMode==="all",(n.inner||[]).reduce(function(i,l){if(i[l.path]||(i[l.path]={message:l.message,type:l.type}),u){var p=i[l.path].types,v=p&&p[l.type];i[l.path]=L(l.path,u,i,l.type,v?[].concat(v,l.message):l.message)}return i},{})),c)};var n,u}))}catch(m){return Promise.reject(m)}}}const se=({label:a,name:t,onValidationChange:r})=>{var R,_;const{t:d}=y(),{mutate:f,isPending:c}=W(),[m,n]=x.useState(!1),[u,i]=x.useState(null),{register:l,watch:p,formState:{errors:v}}=A(),o=p(t),h=x.useCallback(b=>b?/^[a-zA-Z0-9]+$/.test(b)||d("serialNumberError"):!0,[d]),s=x.useCallback(b=>{b&&f(b,{onSuccess:N=>{n(N.success===!0),i(null),r==null||r(N.success===!0)},onError:N=>{var T,E;n(!1),i(((E=(T=N==null?void 0:N.response)==null?void 0:T.data)==null?void 0:E.message)||d("serialNumberError")),r==null||r(!1)}})},[f,r,d]);x.useEffect(()=>{if(!o){n(!1),i(null),r==null||r(!1);return}const b=h(o);if(b!==!0){n(!1),i(b),r==null||r(!1);return}s(o)},[o,h,s,r]);const g=((_=(R=v[t])==null?void 0:R.message)==null?void 0:_.toString())||u;return e.jsxs("div",{className:"space-y-2",children:[e.jsx(D,{htmlFor:t,children:a}),e.jsx(U,{type:"text",id:t,autoComplete:"off",...l(t),placeholder:d("enterSN")}),e.jsxs("div",{className:"min-h-6 mt-1.5",children:[c&&e.jsxs("div",{className:"flex items-center text-sm text-muted-foreground",children:[e.jsx(Y,{className:"mr-2 h-4 w-4 animate-spin"}),d("validating")]}),!c&&m&&e.jsxs("div",{className:"flex items-center text-sm text-green-600",children:[e.jsx(C,{className:"mr-1.5 h-4 w-4"}),d("serialNumberValidated")]}),g&&!c&&e.jsxs("div",{className:"flex items-center text-sm text-destructive",children:[e.jsx(w,{className:"mr-1.5 h-4 w-4"}),d(g)]})]})]})},te=({label:a,name:t})=>{var v,o;const{t:r}=y(),{register:d,formState:{errors:f},setValue:c,watch:m}=A(),n=[{value:"technician",label:r("technician")},{value:"client",label:r("client")},{value:"centralWarehouse",label:r("centralWarehouse")},{value:"SAPVirtualWarehouse",label:r("SAPVirtualWarehouse")},{value:"op",label:r("op")},{value:"remove",label:r("removeTransfer")}],u=!!f[t],i=(o=(v=f[t])==null?void 0:v.message)==null?void 0:o.toString(),l=m(t),p=h=>{c(t,h,{shouldValidate:!0})};return e.jsxs("div",{className:"space-y-2",children:[e.jsx(D,{htmlFor:t,children:a}),e.jsxs(G,{value:l||void 0,onValueChange:p,children:[e.jsx(Q,{className:`w-full ${u?"border-destructive focus-visible:ring-destructive/20":""}`,"aria-invalid":u,children:e.jsx(X,{placeholder:`-- ${r("selectAnOption")} --`})}),e.jsx(Z,{children:n.map(h=>e.jsx(H,{value:h.value,children:h.label},h.value))})]}),e.jsx("input",{type:"hidden",...d(t),value:l||""}),u&&e.jsxs("div",{className:"mt-1.5 flex items-center text-sm text-destructive",children:[e.jsx(w,{className:"mr-1.5 h-4 w-4"}),r(i||"")]})]})},ae=J("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function S({className:a,variant:t,...r}){return e.jsx("div",{"data-slot":"alert",role:"alert",className:O(ae({variant:t}),a),...r})}function q({className:a,...t}){return e.jsx("div",{"data-slot":"alert-description",className:O("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",a),...t})}const ie=x.lazy(()=>I(()=>import("./Technician-C95efZXi.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13]))),le=x.lazy(()=>I(()=>import("./ServiceId-BO7rki53.js"),__vite__mapDeps([14,1,2,3,4,5,6,7,8,9,10,11]))),ce=x.lazy(()=>I(()=>import("./Op-7awmWtOv.js"),__vite__mapDeps([15,1,2,3,4,5,6,7,8,9,10,11,12,13]))),ne=x.lazy(()=>I(()=>import("./Remove-Ct6VhGhL.js"),__vite__mapDeps([16,1,2,3,4,5,6,7,8,9,10,11]))),je=()=>{const{t:a}=y(),t=M().shape({equipmentSerialNum:j().required(a("serialNumberRequired")),transferTo:j().required("transferToRequired"),nameOfTechnician:j().when("transferTo",{is:"technician",then:s=>s.required("technicianNameRequired"),otherwise:s=>s.notRequired()}).required("technicianNameRequired"),serviceId:j().when("transferTo",{is:"client",then:s=>s.required("serviceIdRequired"),otherwise:s=>s.notRequired()}).required("serviceIdRequired"),userId:j().when("transferTo",{is:"op",then:s=>s.required("opIdRequired"),otherwise:s=>s.notRequired()}).required("opIdRequired"),deliveryShop:j().when("transferTo",{is:"op",then:s=>s.required("deliveryShopRequired"),otherwise:s=>s.notRequired()}).required("opIdRequired"),deleteId:j().when("transferTo",{is:"remove",then:s=>s.required("deleteIdDateRequired"),otherwise:s=>s.notRequired()}).required("deleteIdDateRequired"),deleteItemId:j().when("transferTo",{is:"remove",then:s=>s.required("deleteItemIdDateRequired"),otherwise:s=>s.notRequired()}).required("deleteItemIdDateRequired")}),[r,d]=x.useState(!1),[f,c]=x.useState(null),{mutate:m,isPending:n}=k(),{mutate:u,isPending:i}=F(),l=$({resolver:re(t),mode:"onChange"}),{handleSubmit:p,watch:v}=l,o=v("transferTo"),h=async s=>{if(o=="remove"){const g={id:Number(s.deleteId),itemId:Number(s.deleteItemId)};u(g,{onSuccess:()=>{c(!0),l.reset()},onError:()=>{c(!1)}});return}else m(s,{onSuccess:()=>{c(!0),l.reset()},onError:()=>{c(!1)}})};return e.jsx("div",{className:"flex items-center justify-center p-4 min-h-[calc(100vh-65px)]",children:e.jsxs("div",{className:"w-full max-w-md overflow-hidden rounded-2xl shadow-xl border",children:[e.jsx("div",{className:"bg-primary-foreground px-8 py-5",children:e.jsx("h2",{className:"text-xl font-bold ",children:a("transferCorrection")})})," ",e.jsxs("div",{className:"px-8 py-6",children:[f===!0&&e.jsxs(S,{className:"mb-6 border-green-200 bg-green-50",children:[e.jsx(ee,{className:"h-4 w-4 text-green-600"}),e.jsx(q,{className:"text-green-700",children:a("transferSuccessful")})]}),f===!1&&e.jsxs(S,{variant:"destructive",className:"mb-6",children:[e.jsx(w,{className:"h-4 w-4"}),e.jsx(q,{children:a("transferFailed")})]}),e.jsx(B,{...l,children:e.jsxs("form",{onSubmit:p(h),className:"space-y-5",children:[e.jsx(se,{label:a("serialNumber"),name:"equipmentSerialNum",onValidationChange:d}),r&&e.jsx(te,{label:a("transferTo"),name:"transferTo"}),e.jsxs(x.Suspense,{fallback:e.jsx("div",{children:a("loading")}),children:[o==="technician"&&e.jsx(ie,{}),o==="client"&&e.jsx(le,{}),o==="centralWarehouse"&&e.jsxs(S,{className:"border text-blue-600 dark:text-blue-500",children:[e.jsx(P,{}),e.jsx(q,{className:"text-blue-600 dark:text-blue-500",children:a("transferToCentralWarehouse")})]}),o==="SAPVirtualWarehouse"&&e.jsxs(S,{className:"border text-blue-600 dark:text-blue-500",children:[e.jsx(P,{}),e.jsx(q,{className:"text-blue-600 dark:text-blue-500",children:a("transferSAPVirtualWarehouse")})]}),o==="op"&&e.jsx(ce,{}),o==="remove"&&e.jsx(ne,{})]}),o&&e.jsx(K,{type:"submit",disabled:n||i,className:`w-full rounded-lg px-4 py-3 text-sm font-semibold transition-all 
                    ${n||i?"cursor-not-allowed":"focus:ring-4 focus:ring-indigo-500/20"}`,children:n||i?e.jsxs("div",{className:"flex items-center justify-center",children:[e.jsx("div",{className:"h-4 w-4 animate-spin rounded-full border-2  border-t-transparent mr-2"}),a("processing")]}):a("submit")})]})})]})]})})};export{je as default};
