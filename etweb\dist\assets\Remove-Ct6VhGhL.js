import{j as e}from"./query-vendor-DbpRcGHB.js";import{u as N}from"./form-vendor-mwNakpFF.js";import{f as b}from"./index-CvPR-Eda.js";import{r as y}from"./router-vendor-CX6yTN5-.js";import{M as I}from"./ui-components-B3WDh88j.js";import{u as D}from"./i18n-vendor-CzM1WOJE.js";import{f as u}from"./ui-vendor-BwzdWZX0.js";import"./react-vendor-DJG_os-6.js";import"./api-client-xsH4HHeE.js";import"./etws-api-BUsVSvPp.js";const B=()=>{var c,x;const{t:r}=D(),{register:f,watch:m,setValue:l,formState:{errors:n}}=N(),h=m("equipmentSerialNum"),{data:s,isLoading:d,error:o}=b(h),j=!!n.removalDate,v=(x=(c=n.removalDate)==null?void 0:c.message)==null?void 0:x.toString(),g=m("deleteId"),t=s==null?void 0:s.find(a=>a.id===Number(g));return y.useEffect(()=>{t!=null&&t.itemId&&l("deleteItemId",t.itemId)},[t,l]),e.jsxs("div",{className:"space-y-2",children:[e.jsx(I,{htmlFor:"removalDate",children:r("selectRemovalDate")}),e.jsxs("div",{children:[d?e.jsxs("div",{className:"flex items-center space-x-2 py-3",children:[e.jsx("div",{className:"h-4 w-4 animate-spin rounded-full border-2 border-indigo-500 border-t-transparent"}),e.jsx("span",{className:"text-sm text-gray-500",children:r("loading")})]}):o?e.jsxs("div",{className:"flex items-center rounded-lg border border-red-200 bg-red-50 px-4 py-3 text-sm text-destructive",children:[e.jsx(u,{className:"mr-2 h-5 w-5"}),e.jsx("span",{children:(()=>{var i,p;return((p=(i=o.response)==null?void 0:i.data)==null?void 0:p.message)||o.message||r("errorOccurred")})()})]}):e.jsxs("select",{id:"deleteId",...f("deleteId"),className:"w-full rounded-lg border px-4 py-3 text-sm outline-none transition-all focus:ring-2",children:[e.jsx("option",{value:"",children:r("pleaseSelectADate")}),s?s.map((a,i)=>e.jsx("option",{value:a.id,children:a.insertDate},i)):"No dates available"]}),j&&!d&&!o&&e.jsxs("div",{className:"mt-1.5 flex items-center text-sm text-destructive",children:[e.jsx(u,{className:"mr-1.5 h-4 w-4"}),r(v||"")]})]})]})};export{B as default};
