import { useFormContext } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { useState, useCallback } from "react";
import { Label } from "@/components/ui/label";
import { AlertCircle } from "lucide-react";
import { FilteredDropdown, type DropdownOption } from "@/components/ui/filtered-dropdown";
import { IncorrectEquipmentApi } from "../../data/etws";

const Technician = () => {
  const { t } = useTranslation();
  const [selectedTechnician, setSelectedTechnician] = useState("");
  const {
    register,
    setValue,
    watch,
    formState: { errors },
  } = useFormContext();

  const hasError = !!errors.nameOfTechnician;
  const errorMessage = errors.nameOfTechnician?.message?.toString();

  // Get current form value
  const currentValue = watch("nameOfTechnician") || selectedTechnician;

  // Create API instance
  const incorrectEquipmentApi = new IncorrectEquipmentApi(undefined, "/et-ws", undefined);

  // API search function for FilteredDropdown
  const searchTechnicians = useCallback(async (query: string): Promise<DropdownOption[]> => {
    try {
      if (!query || query.length < 2) return [];

      const response = await incorrectEquipmentApi.apiIncorrectEquipmentGetUserDisplayNamesByNamePrefixGet(query);
      const technicians = response.data || [];

      return technicians.map((tech: any) => ({
        value: tech.displayName || "",
        label: `${tech.displayName} - ${tech.iptuName} - ${tech.eln}`,
        group: tech.iptuName || "Other",
        // Store original data for later use
        originalData: tech
      }));
    } catch (error) {
      console.error('Technician search failed:', error);
      return [];
    }
  }, [incorrectEquipmentApi]);

  const handleTechnicianChange = (value: string | string[]) => {
    const selectedValue = Array.isArray(value) ? value[0] : value;
    setSelectedTechnician(selectedValue);
    setValue("nameOfTechnician", selectedValue, { shouldValidate: true });
  };

  return (
    <div className="space-y-2">
      <Label htmlFor="nameOfTechnician">{t("enterFirstAndLastName")}</Label>

      <FilteredDropdown
        filterType="api"
        onSearch={searchTechnicians}
        value={currentValue}
        onValueChange={handleTechnicianChange}
        placeholder={t("searchTechnicians")}
        searchPlaceholder={t("searchTechnicians")}
        emptyText={t("noTechniciansFound")}
        loadingText={t("loading")}
        minSearchLength={2}
        searchDebounceMs={300}
        groupBy="group"
        clearable
        triggerClassName={hasError ? "border-destructive" : ""}
      />

      <input
        type="hidden"
        {...register("nameOfTechnician")}
        value={currentValue}
      />

      {hasError && (
        <div className="flex items-center text-sm text-destructive">
          <AlertCircle className="h-4 w-4 mr-1.5" />
          {t(errorMessage || "")}
        </div>
      )}
    </div>
  );
};

export default Technician;
