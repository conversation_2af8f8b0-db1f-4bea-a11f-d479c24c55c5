import { useFormContext } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { useGetTechnicianName } from "../../data/query";
import { useState, useMemo } from "react";
import { Label } from "@/components/ui/label";
import { AlertCircle } from "lucide-react";
import { FilteredDropdown, type DropdownOption } from "@/components/ui/filtered-dropdown";

const Technician = () => {
  const { t } = useTranslation();
  const [query, setQuery] = useState("");
  const {
    register,
    setValue,
    watch,
    formState: { errors },
  } = useFormContext();
  const { data: technicianOptions, isLoading } = useGetTechnicianName(query);

  const hasError = !!errors.nameOfTechnician;
  const errorMessage = errors.nameOfTechnician?.message?.toString();

  // Get current form value
  const currentValue = watch("nameOfTechnician") || "";

  // Convert API data to DropdownOption format
  const dropdownOptions: DropdownOption[] = useMemo(() => {
    if (!technicianOptions) return [];

    return technicianOptions.map((tech) => ({
      value: tech.displayName || "",
      label: `${tech.displayName} - ${tech.iptuName} - ${tech.eln}`,
      group: tech.iptuName || "Other",
      originalData: tech
    }));
  }, [technicianOptions]);

  const handleTechnicianChange = (value: string | string[]) => {
    const selectedValue = Array.isArray(value) ? value[0] : value;
    setValue("nameOfTechnician", selectedValue, { shouldValidate: true });
  };

  return (
    <div className="space-y-2">
      <Label htmlFor="nameOfTechnician">{t("enterFirstAndLastName")}</Label>

      <FilteredDropdown
        filterType="external"
        options={dropdownOptions}
        value={currentValue}
        onValueChange={handleTechnicianChange}
        placeholder={t("searchTechnicians")}
        searchPlaceholder={t("searchTechnicians")}
        emptyText={t("noTechniciansFound")}
        loadingText={t("loading")}
        searchDebounceMs={3000}
        groupBy="group"
        clearable
        loading={isLoading}
        searchQuery={query}
        onSearchQueryChange={setQuery}
        triggerClassName={hasError ? "border-destructive" : ""}
      />

      <input
        type="hidden"
        {...register("nameOfTechnician")}
        value={currentValue}
      />

      {hasError && (
        <div className="flex items-center text-sm text-destructive">
          <AlertCircle className="h-4 w-4 mr-1.5" />
          {t(errorMessage || "")}
        </div>
      )}
    </div>
  );
};

export default Technician;
