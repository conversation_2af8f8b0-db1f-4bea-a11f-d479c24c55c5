import{j as g}from"./query-vendor-DbpRcGHB.js";import{r as l,a as qn,R as ke}from"./router-vendor-CX6yTN5-.js";import{C as Zn,a as Fr,b as zr,X as Wr}from"./ui-vendor-BwzdWZX0.js";import{a as Br,g as $r}from"./react-vendor-DJG_os-6.js";function Sn(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function Qn(...e){return t=>{let n=!1;const o=e.map(r=>{const s=Sn(r,t);return!n&&typeof s=="function"&&(n=!0),s});if(n)return()=>{for(let r=0;r<o.length;r++){const s=o[r];typeof s=="function"?s():Sn(e[r],null)}}}}function Q(...e){return l.useCallback(Qn(...e),e)}function $e(e){const t=Hr(e),n=l.forwardRef((o,r)=>{const{children:s,...i}=o,a=l.Children.toArray(s),c=a.find(Gr);if(c){const u=c.props.children,f=a.map(p=>p===c?l.Children.count(u)>1?l.Children.only(null):l.isValidElement(u)?u.props.children:null:p);return g.jsx(t,{...i,ref:r,children:l.isValidElement(u)?l.cloneElement(u,void 0,f):null})}return g.jsx(t,{...i,ref:r,children:s})});return n.displayName=`${e}.Slot`,n}var Vr=$e("Slot");function Hr(e){const t=l.forwardRef((n,o)=>{const{children:r,...s}=n;if(l.isValidElement(r)){const i=Yr(r),a=Kr(s,r.props);return r.type!==l.Fragment&&(a.ref=o?Qn(o,i):i),l.cloneElement(r,a)}return l.Children.count(r)>1?l.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var Ur=Symbol("radix.slottable");function Gr(e){return l.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===Ur}function Kr(e,t){const n={...t};for(const o in t){const r=e[o],s=t[o];/^on[A-Z]/.test(o)?r&&s?n[o]=(...a)=>{const c=s(...a);return r(...a),c}:r&&(n[o]=r):o==="style"?n[o]={...r,...s}:o==="className"&&(n[o]=[r,s].filter(Boolean).join(" "))}return{...e,...n}}function Yr(e){var o,r;let t=(o=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(r=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function Jn(e){var t,n,o="";if(typeof e=="string"||typeof e=="number")o+=e;else if(typeof e=="object")if(Array.isArray(e)){var r=e.length;for(t=0;t<r;t++)e[t]&&(n=Jn(e[t]))&&(o&&(o+=" "),o+=n)}else for(n in e)e[n]&&(o&&(o+=" "),o+=n);return o}function eo(){for(var e,t,n=0,o="",r=arguments.length;n<r;n++)(e=arguments[n])&&(t=Jn(e))&&(o&&(o+=" "),o+=t);return o}const Cn=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,En=eo,Xr=(e,t)=>n=>{var o;if((t==null?void 0:t.variants)==null)return En(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:r,defaultVariants:s}=t,i=Object.keys(r).map(u=>{const f=n==null?void 0:n[u],p=s==null?void 0:s[u];if(f===null)return null;const m=Cn(f)||Cn(p);return r[u][m]}),a=n&&Object.entries(n).reduce((u,f)=>{let[p,m]=f;return m===void 0||(u[p]=m),u},{}),c=t==null||(o=t.compoundVariants)===null||o===void 0?void 0:o.reduce((u,f)=>{let{class:p,className:m,...h}=f;return Object.entries(h).every(x=>{let[d,v]=x;return Array.isArray(v)?v.includes({...s,...a}[d]):{...s,...a}[d]===v})?[...u,p,m]:u},[]);return En(e,i,c,n==null?void 0:n.class,n==null?void 0:n.className)},on="-",qr=e=>{const t=Qr(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:i=>{const a=i.split(on);return a[0]===""&&a.length!==1&&a.shift(),to(a,t)||Zr(i)},getConflictingClassGroupIds:(i,a)=>{const c=n[i]||[];return a&&o[i]?[...c,...o[i]]:c}}},to=(e,t)=>{var i;if(e.length===0)return t.classGroupId;const n=e[0],o=t.nextPart.get(n),r=o?to(e.slice(1),o):void 0;if(r)return r;if(t.validators.length===0)return;const s=e.join(on);return(i=t.validators.find(({validator:a})=>a(s)))==null?void 0:i.classGroupId},Rn=/^\[(.+)\]$/,Zr=e=>{if(Rn.test(e)){const t=Rn.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},Qr=e=>{const{theme:t,classGroups:n}=e,o={nextPart:new Map,validators:[]};for(const r in n)Vt(n[r],o,r,t);return o},Vt=(e,t,n,o)=>{e.forEach(r=>{if(typeof r=="string"){const s=r===""?t:Pn(t,r);s.classGroupId=n;return}if(typeof r=="function"){if(Jr(r)){Vt(r(o),t,n,o);return}t.validators.push({validator:r,classGroupId:n});return}Object.entries(r).forEach(([s,i])=>{Vt(i,Pn(t,s),n,o)})})},Pn=(e,t)=>{let n=e;return t.split(on).forEach(o=>{n.nextPart.has(o)||n.nextPart.set(o,{nextPart:new Map,validators:[]}),n=n.nextPart.get(o)}),n},Jr=e=>e.isThemeGetter,es=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,o=new Map;const r=(s,i)=>{n.set(s,i),t++,t>e&&(t=0,o=n,n=new Map)};return{get(s){let i=n.get(s);if(i!==void 0)return i;if((i=o.get(s))!==void 0)return r(s,i),i},set(s,i){n.has(s)?n.set(s,i):r(s,i)}}},Ht="!",Ut=":",ts=Ut.length,ns=e=>{const{prefix:t,experimentalParseClassName:n}=e;let o=r=>{const s=[];let i=0,a=0,c=0,u;for(let x=0;x<r.length;x++){let d=r[x];if(i===0&&a===0){if(d===Ut){s.push(r.slice(c,x)),c=x+ts;continue}if(d==="/"){u=x;continue}}d==="["?i++:d==="]"?i--:d==="("?a++:d===")"&&a--}const f=s.length===0?r:r.substring(c),p=os(f),m=p!==f,h=u&&u>c?u-c:void 0;return{modifiers:s,hasImportantModifier:m,baseClassName:p,maybePostfixModifierPosition:h}};if(t){const r=t+Ut,s=o;o=i=>i.startsWith(r)?s(i.substring(r.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:i,maybePostfixModifierPosition:void 0}}if(n){const r=o;o=s=>n({className:s,parseClassName:r})}return o},os=e=>e.endsWith(Ht)?e.substring(0,e.length-1):e.startsWith(Ht)?e.substring(1):e,rs=e=>{const t=Object.fromEntries(e.orderSensitiveModifiers.map(o=>[o,!0]));return o=>{if(o.length<=1)return o;const r=[];let s=[];return o.forEach(i=>{i[0]==="["||t[i]?(r.push(...s.sort(),i),s=[]):s.push(i)}),r.push(...s.sort()),r}},ss=e=>({cache:es(e.cacheSize),parseClassName:ns(e),sortModifiers:rs(e),...qr(e)}),is=/\s+/,as=(e,t)=>{const{parseClassName:n,getClassGroupId:o,getConflictingClassGroupIds:r,sortModifiers:s}=t,i=[],a=e.trim().split(is);let c="";for(let u=a.length-1;u>=0;u-=1){const f=a[u],{isExternal:p,modifiers:m,hasImportantModifier:h,baseClassName:x,maybePostfixModifierPosition:d}=n(f);if(p){c=f+(c.length>0?" "+c:c);continue}let v=!!d,y=o(v?x.substring(0,d):x);if(!y){if(!v){c=f+(c.length>0?" "+c:c);continue}if(y=o(x),!y){c=f+(c.length>0?" "+c:c);continue}v=!1}const b=s(m).join(":"),w=h?b+Ht:b,C=w+y;if(i.includes(C))continue;i.push(C);const R=r(y,v);for(let T=0;T<R.length;++T){const P=R[T];i.push(w+P)}c=f+(c.length>0?" "+c:c)}return c};function cs(){let e=0,t,n,o="";for(;e<arguments.length;)(t=arguments[e++])&&(n=no(t))&&(o&&(o+=" "),o+=n);return o}const no=e=>{if(typeof e=="string")return e;let t,n="";for(let o=0;o<e.length;o++)e[o]&&(t=no(e[o]))&&(n&&(n+=" "),n+=t);return n};function ls(e,...t){let n,o,r,s=i;function i(c){const u=t.reduce((f,p)=>p(f),e());return n=ss(u),o=n.cache.get,r=n.cache.set,s=a,a(c)}function a(c){const u=o(c);if(u)return u;const f=as(c,n);return r(c,f),f}return function(){return s(cs.apply(null,arguments))}}const J=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},oo=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,ro=/^\((?:(\w[\w-]*):)?(.+)\)$/i,us=/^\d+\/\d+$/,ds=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,fs=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,ps=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,ms=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,gs=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,_e=e=>us.test(e),M=e=>!!e&&!Number.isNaN(Number(e)),Se=e=>!!e&&Number.isInteger(Number(e)),It=e=>e.endsWith("%")&&M(e.slice(0,-1)),xe=e=>ds.test(e),hs=()=>!0,vs=e=>fs.test(e)&&!ps.test(e),so=()=>!1,bs=e=>ms.test(e),xs=e=>gs.test(e),ys=e=>!A(e)&&!k(e),ws=e=>He(e,co,so),A=e=>oo.test(e),Ne=e=>He(e,lo,vs),Mt=e=>He(e,Ps,M),An=e=>He(e,io,so),Ss=e=>He(e,ao,xs),st=e=>He(e,uo,bs),k=e=>ro.test(e),Qe=e=>Ue(e,lo),Cs=e=>Ue(e,As),kn=e=>Ue(e,io),Es=e=>Ue(e,co),Rs=e=>Ue(e,ao),it=e=>Ue(e,uo,!0),He=(e,t,n)=>{const o=oo.exec(e);return o?o[1]?t(o[1]):n(o[2]):!1},Ue=(e,t,n=!1)=>{const o=ro.exec(e);return o?o[1]?t(o[1]):n:!1},io=e=>e==="position"||e==="percentage",ao=e=>e==="image"||e==="url",co=e=>e==="length"||e==="size"||e==="bg-size",lo=e=>e==="length",Ps=e=>e==="number",As=e=>e==="family-name",uo=e=>e==="shadow",ks=()=>{const e=J("color"),t=J("font"),n=J("text"),o=J("font-weight"),r=J("tracking"),s=J("leading"),i=J("breakpoint"),a=J("container"),c=J("spacing"),u=J("radius"),f=J("shadow"),p=J("inset-shadow"),m=J("text-shadow"),h=J("drop-shadow"),x=J("blur"),d=J("perspective"),v=J("aspect"),y=J("ease"),b=J("animate"),w=()=>["auto","avoid","all","avoid-page","page","left","right","column"],C=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],R=()=>[...C(),k,A],T=()=>["auto","hidden","clip","visible","scroll"],P=()=>["auto","contain","none"],E=()=>[k,A,c],I=()=>[_e,"full","auto",...E()],L=()=>[Se,"none","subgrid",k,A],V=()=>["auto",{span:["full",Se,k,A]},Se,k,A],B=()=>[Se,"auto",k,A],z=()=>["auto","min","max","fr",k,A],_=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],$=()=>["start","end","center","stretch","center-safe","end-safe"],O=()=>["auto",...E()],D=()=>[_e,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...E()],S=()=>[e,k,A],H=()=>[...C(),kn,An,{position:[k,A]}],ee=()=>["no-repeat",{repeat:["","x","y","space","round"]}],se=()=>["auto","cover","contain",Es,ws,{size:[k,A]}],be=()=>[It,Qe,Ne],q=()=>["","none","full",u,k,A],X=()=>["",M,Qe,Ne],ie=()=>["solid","dashed","dotted","double"],de=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],N=()=>[M,It,kn,An],U=()=>["","none",x,k,A],Z=()=>["none",M,k,A],W=()=>["none",M,k,A],F=()=>[M,k,A],j=()=>[_e,"full",...E()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[xe],breakpoint:[xe],color:[hs],container:[xe],"drop-shadow":[xe],ease:["in","out","in-out"],font:[ys],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[xe],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[xe],shadow:[xe],spacing:["px",M],text:[xe],"text-shadow":[xe],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",_e,A,k,v]}],container:["container"],columns:[{columns:[M,A,k,a]}],"break-after":[{"break-after":w()}],"break-before":[{"break-before":w()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:R()}],overflow:[{overflow:T()}],"overflow-x":[{"overflow-x":T()}],"overflow-y":[{"overflow-y":T()}],overscroll:[{overscroll:P()}],"overscroll-x":[{"overscroll-x":P()}],"overscroll-y":[{"overscroll-y":P()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:I()}],"inset-x":[{"inset-x":I()}],"inset-y":[{"inset-y":I()}],start:[{start:I()}],end:[{end:I()}],top:[{top:I()}],right:[{right:I()}],bottom:[{bottom:I()}],left:[{left:I()}],visibility:["visible","invisible","collapse"],z:[{z:[Se,"auto",k,A]}],basis:[{basis:[_e,"full","auto",a,...E()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[M,_e,"auto","initial","none",A]}],grow:[{grow:["",M,k,A]}],shrink:[{shrink:["",M,k,A]}],order:[{order:[Se,"first","last","none",k,A]}],"grid-cols":[{"grid-cols":L()}],"col-start-end":[{col:V()}],"col-start":[{"col-start":B()}],"col-end":[{"col-end":B()}],"grid-rows":[{"grid-rows":L()}],"row-start-end":[{row:V()}],"row-start":[{"row-start":B()}],"row-end":[{"row-end":B()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":z()}],"auto-rows":[{"auto-rows":z()}],gap:[{gap:E()}],"gap-x":[{"gap-x":E()}],"gap-y":[{"gap-y":E()}],"justify-content":[{justify:[..._(),"normal"]}],"justify-items":[{"justify-items":[...$(),"normal"]}],"justify-self":[{"justify-self":["auto",...$()]}],"align-content":[{content:["normal",..._()]}],"align-items":[{items:[...$(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...$(),{baseline:["","last"]}]}],"place-content":[{"place-content":_()}],"place-items":[{"place-items":[...$(),"baseline"]}],"place-self":[{"place-self":["auto",...$()]}],p:[{p:E()}],px:[{px:E()}],py:[{py:E()}],ps:[{ps:E()}],pe:[{pe:E()}],pt:[{pt:E()}],pr:[{pr:E()}],pb:[{pb:E()}],pl:[{pl:E()}],m:[{m:O()}],mx:[{mx:O()}],my:[{my:O()}],ms:[{ms:O()}],me:[{me:O()}],mt:[{mt:O()}],mr:[{mr:O()}],mb:[{mb:O()}],ml:[{ml:O()}],"space-x":[{"space-x":E()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":E()}],"space-y-reverse":["space-y-reverse"],size:[{size:D()}],w:[{w:[a,"screen",...D()]}],"min-w":[{"min-w":[a,"screen","none",...D()]}],"max-w":[{"max-w":[a,"screen","none","prose",{screen:[i]},...D()]}],h:[{h:["screen","lh",...D()]}],"min-h":[{"min-h":["screen","lh","none",...D()]}],"max-h":[{"max-h":["screen","lh",...D()]}],"font-size":[{text:["base",n,Qe,Ne]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,k,Mt]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",It,A]}],"font-family":[{font:[Cs,A,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[r,k,A]}],"line-clamp":[{"line-clamp":[M,"none",k,Mt]}],leading:[{leading:[s,...E()]}],"list-image":[{"list-image":["none",k,A]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",k,A]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:S()}],"text-color":[{text:S()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ie(),"wavy"]}],"text-decoration-thickness":[{decoration:[M,"from-font","auto",k,Ne]}],"text-decoration-color":[{decoration:S()}],"underline-offset":[{"underline-offset":[M,"auto",k,A]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:E()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",k,A]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",k,A]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:H()}],"bg-repeat":[{bg:ee()}],"bg-size":[{bg:se()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Se,k,A],radial:["",k,A],conic:[Se,k,A]},Rs,Ss]}],"bg-color":[{bg:S()}],"gradient-from-pos":[{from:be()}],"gradient-via-pos":[{via:be()}],"gradient-to-pos":[{to:be()}],"gradient-from":[{from:S()}],"gradient-via":[{via:S()}],"gradient-to":[{to:S()}],rounded:[{rounded:q()}],"rounded-s":[{"rounded-s":q()}],"rounded-e":[{"rounded-e":q()}],"rounded-t":[{"rounded-t":q()}],"rounded-r":[{"rounded-r":q()}],"rounded-b":[{"rounded-b":q()}],"rounded-l":[{"rounded-l":q()}],"rounded-ss":[{"rounded-ss":q()}],"rounded-se":[{"rounded-se":q()}],"rounded-ee":[{"rounded-ee":q()}],"rounded-es":[{"rounded-es":q()}],"rounded-tl":[{"rounded-tl":q()}],"rounded-tr":[{"rounded-tr":q()}],"rounded-br":[{"rounded-br":q()}],"rounded-bl":[{"rounded-bl":q()}],"border-w":[{border:X()}],"border-w-x":[{"border-x":X()}],"border-w-y":[{"border-y":X()}],"border-w-s":[{"border-s":X()}],"border-w-e":[{"border-e":X()}],"border-w-t":[{"border-t":X()}],"border-w-r":[{"border-r":X()}],"border-w-b":[{"border-b":X()}],"border-w-l":[{"border-l":X()}],"divide-x":[{"divide-x":X()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":X()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ie(),"hidden","none"]}],"divide-style":[{divide:[...ie(),"hidden","none"]}],"border-color":[{border:S()}],"border-color-x":[{"border-x":S()}],"border-color-y":[{"border-y":S()}],"border-color-s":[{"border-s":S()}],"border-color-e":[{"border-e":S()}],"border-color-t":[{"border-t":S()}],"border-color-r":[{"border-r":S()}],"border-color-b":[{"border-b":S()}],"border-color-l":[{"border-l":S()}],"divide-color":[{divide:S()}],"outline-style":[{outline:[...ie(),"none","hidden"]}],"outline-offset":[{"outline-offset":[M,k,A]}],"outline-w":[{outline:["",M,Qe,Ne]}],"outline-color":[{outline:S()}],shadow:[{shadow:["","none",f,it,st]}],"shadow-color":[{shadow:S()}],"inset-shadow":[{"inset-shadow":["none",p,it,st]}],"inset-shadow-color":[{"inset-shadow":S()}],"ring-w":[{ring:X()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:S()}],"ring-offset-w":[{"ring-offset":[M,Ne]}],"ring-offset-color":[{"ring-offset":S()}],"inset-ring-w":[{"inset-ring":X()}],"inset-ring-color":[{"inset-ring":S()}],"text-shadow":[{"text-shadow":["none",m,it,st]}],"text-shadow-color":[{"text-shadow":S()}],opacity:[{opacity:[M,k,A]}],"mix-blend":[{"mix-blend":[...de(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":de()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[M]}],"mask-image-linear-from-pos":[{"mask-linear-from":N()}],"mask-image-linear-to-pos":[{"mask-linear-to":N()}],"mask-image-linear-from-color":[{"mask-linear-from":S()}],"mask-image-linear-to-color":[{"mask-linear-to":S()}],"mask-image-t-from-pos":[{"mask-t-from":N()}],"mask-image-t-to-pos":[{"mask-t-to":N()}],"mask-image-t-from-color":[{"mask-t-from":S()}],"mask-image-t-to-color":[{"mask-t-to":S()}],"mask-image-r-from-pos":[{"mask-r-from":N()}],"mask-image-r-to-pos":[{"mask-r-to":N()}],"mask-image-r-from-color":[{"mask-r-from":S()}],"mask-image-r-to-color":[{"mask-r-to":S()}],"mask-image-b-from-pos":[{"mask-b-from":N()}],"mask-image-b-to-pos":[{"mask-b-to":N()}],"mask-image-b-from-color":[{"mask-b-from":S()}],"mask-image-b-to-color":[{"mask-b-to":S()}],"mask-image-l-from-pos":[{"mask-l-from":N()}],"mask-image-l-to-pos":[{"mask-l-to":N()}],"mask-image-l-from-color":[{"mask-l-from":S()}],"mask-image-l-to-color":[{"mask-l-to":S()}],"mask-image-x-from-pos":[{"mask-x-from":N()}],"mask-image-x-to-pos":[{"mask-x-to":N()}],"mask-image-x-from-color":[{"mask-x-from":S()}],"mask-image-x-to-color":[{"mask-x-to":S()}],"mask-image-y-from-pos":[{"mask-y-from":N()}],"mask-image-y-to-pos":[{"mask-y-to":N()}],"mask-image-y-from-color":[{"mask-y-from":S()}],"mask-image-y-to-color":[{"mask-y-to":S()}],"mask-image-radial":[{"mask-radial":[k,A]}],"mask-image-radial-from-pos":[{"mask-radial-from":N()}],"mask-image-radial-to-pos":[{"mask-radial-to":N()}],"mask-image-radial-from-color":[{"mask-radial-from":S()}],"mask-image-radial-to-color":[{"mask-radial-to":S()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":C()}],"mask-image-conic-pos":[{"mask-conic":[M]}],"mask-image-conic-from-pos":[{"mask-conic-from":N()}],"mask-image-conic-to-pos":[{"mask-conic-to":N()}],"mask-image-conic-from-color":[{"mask-conic-from":S()}],"mask-image-conic-to-color":[{"mask-conic-to":S()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:H()}],"mask-repeat":[{mask:ee()}],"mask-size":[{mask:se()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",k,A]}],filter:[{filter:["","none",k,A]}],blur:[{blur:U()}],brightness:[{brightness:[M,k,A]}],contrast:[{contrast:[M,k,A]}],"drop-shadow":[{"drop-shadow":["","none",h,it,st]}],"drop-shadow-color":[{"drop-shadow":S()}],grayscale:[{grayscale:["",M,k,A]}],"hue-rotate":[{"hue-rotate":[M,k,A]}],invert:[{invert:["",M,k,A]}],saturate:[{saturate:[M,k,A]}],sepia:[{sepia:["",M,k,A]}],"backdrop-filter":[{"backdrop-filter":["","none",k,A]}],"backdrop-blur":[{"backdrop-blur":U()}],"backdrop-brightness":[{"backdrop-brightness":[M,k,A]}],"backdrop-contrast":[{"backdrop-contrast":[M,k,A]}],"backdrop-grayscale":[{"backdrop-grayscale":["",M,k,A]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[M,k,A]}],"backdrop-invert":[{"backdrop-invert":["",M,k,A]}],"backdrop-opacity":[{"backdrop-opacity":[M,k,A]}],"backdrop-saturate":[{"backdrop-saturate":[M,k,A]}],"backdrop-sepia":[{"backdrop-sepia":["",M,k,A]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":E()}],"border-spacing-x":[{"border-spacing-x":E()}],"border-spacing-y":[{"border-spacing-y":E()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",k,A]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[M,"initial",k,A]}],ease:[{ease:["linear","initial",y,k,A]}],delay:[{delay:[M,k,A]}],animate:[{animate:["none",b,k,A]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[d,k,A]}],"perspective-origin":[{"perspective-origin":R()}],rotate:[{rotate:Z()}],"rotate-x":[{"rotate-x":Z()}],"rotate-y":[{"rotate-y":Z()}],"rotate-z":[{"rotate-z":Z()}],scale:[{scale:W()}],"scale-x":[{"scale-x":W()}],"scale-y":[{"scale-y":W()}],"scale-z":[{"scale-z":W()}],"scale-3d":["scale-3d"],skew:[{skew:F()}],"skew-x":[{"skew-x":F()}],"skew-y":[{"skew-y":F()}],transform:[{transform:[k,A,"","none","gpu","cpu"]}],"transform-origin":[{origin:R()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:j()}],"translate-x":[{"translate-x":j()}],"translate-y":[{"translate-y":j()}],"translate-z":[{"translate-z":j()}],"translate-none":["translate-none"],accent:[{accent:S()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:S()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",k,A]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":E()}],"scroll-mx":[{"scroll-mx":E()}],"scroll-my":[{"scroll-my":E()}],"scroll-ms":[{"scroll-ms":E()}],"scroll-me":[{"scroll-me":E()}],"scroll-mt":[{"scroll-mt":E()}],"scroll-mr":[{"scroll-mr":E()}],"scroll-mb":[{"scroll-mb":E()}],"scroll-ml":[{"scroll-ml":E()}],"scroll-p":[{"scroll-p":E()}],"scroll-px":[{"scroll-px":E()}],"scroll-py":[{"scroll-py":E()}],"scroll-ps":[{"scroll-ps":E()}],"scroll-pe":[{"scroll-pe":E()}],"scroll-pt":[{"scroll-pt":E()}],"scroll-pr":[{"scroll-pr":E()}],"scroll-pb":[{"scroll-pb":E()}],"scroll-pl":[{"scroll-pl":E()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",k,A]}],fill:[{fill:["none",...S()]}],"stroke-w":[{stroke:[M,Qe,Ne,Mt]}],stroke:[{stroke:["none",...S()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},Ns=ls(ks);function Y(...e){return Ns(eo(e))}const Ts=Xr("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function gl({className:e,variant:t,size:n,asChild:o=!1,...r}){const s=o?Vr:"button";return g.jsx(s,{"data-slot":"button",className:Y(Ts({variant:t,size:n,className:e})),...r})}function K(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e==null||e(r),n===!1||!r.defaultPrevented)return t==null?void 0:t(r)}}function Os(e,t){const n=l.createContext(t),o=s=>{const{children:i,...a}=s,c=l.useMemo(()=>a,Object.values(a));return g.jsx(n.Provider,{value:c,children:i})};o.displayName=e+"Provider";function r(s){const i=l.useContext(n);if(i)return i;if(t!==void 0)return t;throw new Error(`\`${s}\` must be used within \`${e}\``)}return[o,r]}function St(e,t=[]){let n=[];function o(s,i){const a=l.createContext(i),c=n.length;n=[...n,i];const u=p=>{var y;const{scope:m,children:h,...x}=p,d=((y=m==null?void 0:m[e])==null?void 0:y[c])||a,v=l.useMemo(()=>x,Object.values(x));return g.jsx(d.Provider,{value:v,children:h})};u.displayName=s+"Provider";function f(p,m){var d;const h=((d=m==null?void 0:m[e])==null?void 0:d[c])||a,x=l.useContext(h);if(x)return x;if(i!==void 0)return i;throw new Error(`\`${p}\` must be used within \`${s}\``)}return[u,f]}const r=()=>{const s=n.map(i=>l.createContext(i));return function(a){const c=(a==null?void 0:a[e])||s;return l.useMemo(()=>({[`__scope${e}`]:{...a,[e]:c}}),[a,c])}};return r.scopeName=e,[o,Is(r,...t)]}function Is(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const o=e.map(r=>({useScope:r(),scopeName:r.scopeName}));return function(s){const i=o.reduce((a,{useScope:c,scopeName:u})=>{const p=c(s)[`__scope${u}`];return{...a,...p}},{});return l.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}var te=globalThis!=null&&globalThis.document?l.useLayoutEffect:()=>{},Ms=qn[" useInsertionEffect ".trim().toString()]||te;function Gt({prop:e,defaultProp:t,onChange:n=()=>{},caller:o}){const[r,s,i]=Ds({defaultProp:t,onChange:n}),a=e!==void 0,c=a?e:r;{const f=l.useRef(e!==void 0);l.useEffect(()=>{const p=f.current;p!==a&&console.warn(`${o} is changing from ${p?"controlled":"uncontrolled"} to ${a?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),f.current=a},[a,o])}const u=l.useCallback(f=>{var p;if(a){const m=_s(f)?f(e):f;m!==e&&((p=i.current)==null||p.call(i,m))}else s(f)},[a,e,s,i]);return[c,u]}function Ds({defaultProp:e,onChange:t}){const[n,o]=l.useState(e),r=l.useRef(n),s=l.useRef(t);return Ms(()=>{s.current=t},[t]),l.useEffect(()=>{var i;r.current!==n&&((i=s.current)==null||i.call(s,n),r.current=n)},[n,r]),[n,o,s]}function _s(e){return typeof e=="function"}var nt=Br();const Ls=$r(nt);var js=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],G=js.reduce((e,t)=>{const n=$e(`Primitive.${t}`),o=l.forwardRef((r,s)=>{const{asChild:i,...a}=r,c=i?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),g.jsx(c,{...a,ref:s})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function Fs(e,t){e&&nt.flushSync(()=>e.dispatchEvent(t))}function zs(e){const t=e+"CollectionProvider",[n,o]=St(t),[r,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),i=d=>{const{scope:v,children:y}=d,b=ke.useRef(null),w=ke.useRef(new Map).current;return g.jsx(r,{scope:v,itemMap:w,collectionRef:b,children:y})};i.displayName=t;const a=e+"CollectionSlot",c=$e(a),u=ke.forwardRef((d,v)=>{const{scope:y,children:b}=d,w=s(a,y),C=Q(v,w.collectionRef);return g.jsx(c,{ref:C,children:b})});u.displayName=a;const f=e+"CollectionItemSlot",p="data-radix-collection-item",m=$e(f),h=ke.forwardRef((d,v)=>{const{scope:y,children:b,...w}=d,C=ke.useRef(null),R=Q(v,C),T=s(f,y);return ke.useEffect(()=>(T.itemMap.set(C,{ref:C,...w}),()=>void T.itemMap.delete(C))),g.jsx(m,{[p]:"",ref:R,children:b})});h.displayName=f;function x(d){const v=s(e+"CollectionConsumer",d);return ke.useCallback(()=>{const b=v.collectionRef.current;if(!b)return[];const w=Array.from(b.querySelectorAll(`[${p}]`));return Array.from(v.itemMap.values()).sort((T,P)=>w.indexOf(T.ref.current)-w.indexOf(P.ref.current))},[v.collectionRef,v.itemMap])}return[{Provider:i,Slot:u,ItemSlot:h},x,o]}var Ws=l.createContext(void 0);function Bs(e){const t=l.useContext(Ws);return e||t||"ltr"}function Te(e){const t=l.useRef(e);return l.useEffect(()=>{t.current=e}),l.useMemo(()=>(...n)=>{var o;return(o=t.current)==null?void 0:o.call(t,...n)},[])}function $s(e,t=globalThis==null?void 0:globalThis.document){const n=Te(e);l.useEffect(()=>{const o=r=>{r.key==="Escape"&&n(r)};return t.addEventListener("keydown",o,{capture:!0}),()=>t.removeEventListener("keydown",o,{capture:!0})},[n,t])}var Vs="DismissableLayer",Kt="dismissableLayer.update",Hs="dismissableLayer.pointerDownOutside",Us="dismissableLayer.focusOutside",Nn,fo=l.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),rn=l.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:o,onPointerDownOutside:r,onFocusOutside:s,onInteractOutside:i,onDismiss:a,...c}=e,u=l.useContext(fo),[f,p]=l.useState(null),m=(f==null?void 0:f.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,h]=l.useState({}),x=Q(t,P=>p(P)),d=Array.from(u.layers),[v]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),y=d.indexOf(v),b=f?d.indexOf(f):-1,w=u.layersWithOutsidePointerEventsDisabled.size>0,C=b>=y,R=Ys(P=>{const E=P.target,I=[...u.branches].some(L=>L.contains(E));!C||I||(r==null||r(P),i==null||i(P),P.defaultPrevented||a==null||a())},m),T=Xs(P=>{const E=P.target;[...u.branches].some(L=>L.contains(E))||(s==null||s(P),i==null||i(P),P.defaultPrevented||a==null||a())},m);return $s(P=>{b===u.layers.size-1&&(o==null||o(P),!P.defaultPrevented&&a&&(P.preventDefault(),a()))},m),l.useEffect(()=>{if(f)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(Nn=m.body.style.pointerEvents,m.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(f)),u.layers.add(f),Tn(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(m.body.style.pointerEvents=Nn)}},[f,m,n,u]),l.useEffect(()=>()=>{f&&(u.layers.delete(f),u.layersWithOutsidePointerEventsDisabled.delete(f),Tn())},[f,u]),l.useEffect(()=>{const P=()=>h({});return document.addEventListener(Kt,P),()=>document.removeEventListener(Kt,P)},[]),g.jsx(G.div,{...c,ref:x,style:{pointerEvents:w?C?"auto":"none":void 0,...e.style},onFocusCapture:K(e.onFocusCapture,T.onFocusCapture),onBlurCapture:K(e.onBlurCapture,T.onBlurCapture),onPointerDownCapture:K(e.onPointerDownCapture,R.onPointerDownCapture)})});rn.displayName=Vs;var Gs="DismissableLayerBranch",Ks=l.forwardRef((e,t)=>{const n=l.useContext(fo),o=l.useRef(null),r=Q(t,o);return l.useEffect(()=>{const s=o.current;if(s)return n.branches.add(s),()=>{n.branches.delete(s)}},[n.branches]),g.jsx(G.div,{...e,ref:r})});Ks.displayName=Gs;function Ys(e,t=globalThis==null?void 0:globalThis.document){const n=Te(e),o=l.useRef(!1),r=l.useRef(()=>{});return l.useEffect(()=>{const s=a=>{if(a.target&&!o.current){let c=function(){po(Hs,n,u,{discrete:!0})};const u={originalEvent:a};a.pointerType==="touch"?(t.removeEventListener("click",r.current),r.current=c,t.addEventListener("click",r.current,{once:!0})):c()}else t.removeEventListener("click",r.current);o.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",s)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",s),t.removeEventListener("click",r.current)}},[t,n]),{onPointerDownCapture:()=>o.current=!0}}function Xs(e,t=globalThis==null?void 0:globalThis.document){const n=Te(e),o=l.useRef(!1);return l.useEffect(()=>{const r=s=>{s.target&&!o.current&&po(Us,n,{originalEvent:s},{discrete:!1})};return t.addEventListener("focusin",r),()=>t.removeEventListener("focusin",r)},[t,n]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}function Tn(){const e=new CustomEvent(Kt);document.dispatchEvent(e)}function po(e,t,n,{discrete:o}){const r=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&r.addEventListener(e,t,{once:!0}),o?Fs(r,s):r.dispatchEvent(s)}var Dt=0;function mo(){l.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??On()),document.body.insertAdjacentElement("beforeend",e[1]??On()),Dt++,()=>{Dt===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),Dt--}},[])}function On(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var _t="focusScope.autoFocusOnMount",Lt="focusScope.autoFocusOnUnmount",In={bubbles:!1,cancelable:!0},qs="FocusScope",sn=l.forwardRef((e,t)=>{const{loop:n=!1,trapped:o=!1,onMountAutoFocus:r,onUnmountAutoFocus:s,...i}=e,[a,c]=l.useState(null),u=Te(r),f=Te(s),p=l.useRef(null),m=Q(t,d=>c(d)),h=l.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;l.useEffect(()=>{if(o){let d=function(w){if(h.paused||!a)return;const C=w.target;a.contains(C)?p.current=C:Ce(p.current,{select:!0})},v=function(w){if(h.paused||!a)return;const C=w.relatedTarget;C!==null&&(a.contains(C)||Ce(p.current,{select:!0}))},y=function(w){if(document.activeElement===document.body)for(const R of w)R.removedNodes.length>0&&Ce(a)};document.addEventListener("focusin",d),document.addEventListener("focusout",v);const b=new MutationObserver(y);return a&&b.observe(a,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",d),document.removeEventListener("focusout",v),b.disconnect()}}},[o,a,h.paused]),l.useEffect(()=>{if(a){Dn.add(h);const d=document.activeElement;if(!a.contains(d)){const y=new CustomEvent(_t,In);a.addEventListener(_t,u),a.dispatchEvent(y),y.defaultPrevented||(Zs(ni(go(a)),{select:!0}),document.activeElement===d&&Ce(a))}return()=>{a.removeEventListener(_t,u),setTimeout(()=>{const y=new CustomEvent(Lt,In);a.addEventListener(Lt,f),a.dispatchEvent(y),y.defaultPrevented||Ce(d??document.body,{select:!0}),a.removeEventListener(Lt,f),Dn.remove(h)},0)}}},[a,u,f,h]);const x=l.useCallback(d=>{if(!n&&!o||h.paused)return;const v=d.key==="Tab"&&!d.altKey&&!d.ctrlKey&&!d.metaKey,y=document.activeElement;if(v&&y){const b=d.currentTarget,[w,C]=Qs(b);w&&C?!d.shiftKey&&y===C?(d.preventDefault(),n&&Ce(w,{select:!0})):d.shiftKey&&y===w&&(d.preventDefault(),n&&Ce(C,{select:!0})):y===b&&d.preventDefault()}},[n,o,h.paused]);return g.jsx(G.div,{tabIndex:-1,...i,ref:m,onKeyDown:x})});sn.displayName=qs;function Zs(e,{select:t=!1}={}){const n=document.activeElement;for(const o of e)if(Ce(o,{select:t}),document.activeElement!==n)return}function Qs(e){const t=go(e),n=Mn(t,e),o=Mn(t.reverse(),e);return[n,o]}function go(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:o=>{const r=o.tagName==="INPUT"&&o.type==="hidden";return o.disabled||o.hidden||r?NodeFilter.FILTER_SKIP:o.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Mn(e,t){for(const n of e)if(!Js(n,{upTo:t}))return n}function Js(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function ei(e){return e instanceof HTMLInputElement&&"select"in e}function Ce(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&ei(e)&&t&&e.select()}}var Dn=ti();function ti(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=_n(e,t),e.unshift(t)},remove(t){var n;e=_n(e,t),(n=e[0])==null||n.resume()}}}function _n(e,t){const n=[...e],o=n.indexOf(t);return o!==-1&&n.splice(o,1),n}function ni(e){return e.filter(t=>t.tagName!=="A")}var oi=qn[" useId ".trim().toString()]||(()=>{}),ri=0;function ze(e){const[t,n]=l.useState(oi());return te(()=>{n(o=>o??String(ri++))},[e]),e||(t?`radix-${t}`:"")}const si=["top","right","bottom","left"],Ee=Math.min,oe=Math.max,ht=Math.round,at=Math.floor,ge=e=>({x:e,y:e}),ii={left:"right",right:"left",bottom:"top",top:"bottom"},ai={start:"end",end:"start"};function Yt(e,t,n){return oe(e,Ee(t,n))}function ye(e,t){return typeof e=="function"?e(t):e}function we(e){return e.split("-")[0]}function Ge(e){return e.split("-")[1]}function an(e){return e==="x"?"y":"x"}function cn(e){return e==="y"?"height":"width"}function me(e){return["top","bottom"].includes(we(e))?"y":"x"}function ln(e){return an(me(e))}function ci(e,t,n){n===void 0&&(n=!1);const o=Ge(e),r=ln(e),s=cn(r);let i=r==="x"?o===(n?"end":"start")?"right":"left":o==="start"?"bottom":"top";return t.reference[s]>t.floating[s]&&(i=vt(i)),[i,vt(i)]}function li(e){const t=vt(e);return[Xt(e),t,Xt(t)]}function Xt(e){return e.replace(/start|end/g,t=>ai[t])}function ui(e,t,n){const o=["left","right"],r=["right","left"],s=["top","bottom"],i=["bottom","top"];switch(e){case"top":case"bottom":return n?t?r:o:t?o:r;case"left":case"right":return t?s:i;default:return[]}}function di(e,t,n,o){const r=Ge(e);let s=ui(we(e),n==="start",o);return r&&(s=s.map(i=>i+"-"+r),t&&(s=s.concat(s.map(Xt)))),s}function vt(e){return e.replace(/left|right|bottom|top/g,t=>ii[t])}function fi(e){return{top:0,right:0,bottom:0,left:0,...e}}function ho(e){return typeof e!="number"?fi(e):{top:e,right:e,bottom:e,left:e}}function bt(e){const{x:t,y:n,width:o,height:r}=e;return{width:o,height:r,top:n,left:t,right:t+o,bottom:n+r,x:t,y:n}}function Ln(e,t,n){let{reference:o,floating:r}=e;const s=me(t),i=ln(t),a=cn(i),c=we(t),u=s==="y",f=o.x+o.width/2-r.width/2,p=o.y+o.height/2-r.height/2,m=o[a]/2-r[a]/2;let h;switch(c){case"top":h={x:f,y:o.y-r.height};break;case"bottom":h={x:f,y:o.y+o.height};break;case"right":h={x:o.x+o.width,y:p};break;case"left":h={x:o.x-r.width,y:p};break;default:h={x:o.x,y:o.y}}switch(Ge(t)){case"start":h[i]-=m*(n&&u?-1:1);break;case"end":h[i]+=m*(n&&u?-1:1);break}return h}const pi=async(e,t,n)=>{const{placement:o="bottom",strategy:r="absolute",middleware:s=[],platform:i}=n,a=s.filter(Boolean),c=await(i.isRTL==null?void 0:i.isRTL(t));let u=await i.getElementRects({reference:e,floating:t,strategy:r}),{x:f,y:p}=Ln(u,o,c),m=o,h={},x=0;for(let d=0;d<a.length;d++){const{name:v,fn:y}=a[d],{x:b,y:w,data:C,reset:R}=await y({x:f,y:p,initialPlacement:o,placement:m,strategy:r,middlewareData:h,rects:u,platform:i,elements:{reference:e,floating:t}});f=b??f,p=w??p,h={...h,[v]:{...h[v],...C}},R&&x<=50&&(x++,typeof R=="object"&&(R.placement&&(m=R.placement),R.rects&&(u=R.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:r}):R.rects),{x:f,y:p}=Ln(u,m,c)),d=-1)}return{x:f,y:p,placement:m,strategy:r,middlewareData:h}};async function et(e,t){var n;t===void 0&&(t={});const{x:o,y:r,platform:s,rects:i,elements:a,strategy:c}=e,{boundary:u="clippingAncestors",rootBoundary:f="viewport",elementContext:p="floating",altBoundary:m=!1,padding:h=0}=ye(t,e),x=ho(h),v=a[m?p==="floating"?"reference":"floating":p],y=bt(await s.getClippingRect({element:(n=await(s.isElement==null?void 0:s.isElement(v)))==null||n?v:v.contextElement||await(s.getDocumentElement==null?void 0:s.getDocumentElement(a.floating)),boundary:u,rootBoundary:f,strategy:c})),b=p==="floating"?{x:o,y:r,width:i.floating.width,height:i.floating.height}:i.reference,w=await(s.getOffsetParent==null?void 0:s.getOffsetParent(a.floating)),C=await(s.isElement==null?void 0:s.isElement(w))?await(s.getScale==null?void 0:s.getScale(w))||{x:1,y:1}:{x:1,y:1},R=bt(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:b,offsetParent:w,strategy:c}):b);return{top:(y.top-R.top+x.top)/C.y,bottom:(R.bottom-y.bottom+x.bottom)/C.y,left:(y.left-R.left+x.left)/C.x,right:(R.right-y.right+x.right)/C.x}}const mi=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:o,placement:r,rects:s,platform:i,elements:a,middlewareData:c}=t,{element:u,padding:f=0}=ye(e,t)||{};if(u==null)return{};const p=ho(f),m={x:n,y:o},h=ln(r),x=cn(h),d=await i.getDimensions(u),v=h==="y",y=v?"top":"left",b=v?"bottom":"right",w=v?"clientHeight":"clientWidth",C=s.reference[x]+s.reference[h]-m[h]-s.floating[x],R=m[h]-s.reference[h],T=await(i.getOffsetParent==null?void 0:i.getOffsetParent(u));let P=T?T[w]:0;(!P||!await(i.isElement==null?void 0:i.isElement(T)))&&(P=a.floating[w]||s.floating[x]);const E=C/2-R/2,I=P/2-d[x]/2-1,L=Ee(p[y],I),V=Ee(p[b],I),B=L,z=P-d[x]-V,_=P/2-d[x]/2+E,$=Yt(B,_,z),O=!c.arrow&&Ge(r)!=null&&_!==$&&s.reference[x]/2-(_<B?L:V)-d[x]/2<0,D=O?_<B?_-B:_-z:0;return{[h]:m[h]+D,data:{[h]:$,centerOffset:_-$-D,...O&&{alignmentOffset:D}},reset:O}}}),gi=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,o;const{placement:r,middlewareData:s,rects:i,initialPlacement:a,platform:c,elements:u}=t,{mainAxis:f=!0,crossAxis:p=!0,fallbackPlacements:m,fallbackStrategy:h="bestFit",fallbackAxisSideDirection:x="none",flipAlignment:d=!0,...v}=ye(e,t);if((n=s.arrow)!=null&&n.alignmentOffset)return{};const y=we(r),b=me(a),w=we(a)===a,C=await(c.isRTL==null?void 0:c.isRTL(u.floating)),R=m||(w||!d?[vt(a)]:li(a)),T=x!=="none";!m&&T&&R.push(...di(a,d,x,C));const P=[a,...R],E=await et(t,v),I=[];let L=((o=s.flip)==null?void 0:o.overflows)||[];if(f&&I.push(E[y]),p){const _=ci(r,i,C);I.push(E[_[0]],E[_[1]])}if(L=[...L,{placement:r,overflows:I}],!I.every(_=>_<=0)){var V,B;const _=(((V=s.flip)==null?void 0:V.index)||0)+1,$=P[_];if($&&(!(p==="alignment"?b!==me($):!1)||L.every(S=>S.overflows[0]>0&&me(S.placement)===b)))return{data:{index:_,overflows:L},reset:{placement:$}};let O=(B=L.filter(D=>D.overflows[0]<=0).sort((D,S)=>D.overflows[1]-S.overflows[1])[0])==null?void 0:B.placement;if(!O)switch(h){case"bestFit":{var z;const D=(z=L.filter(S=>{if(T){const H=me(S.placement);return H===b||H==="y"}return!0}).map(S=>[S.placement,S.overflows.filter(H=>H>0).reduce((H,ee)=>H+ee,0)]).sort((S,H)=>S[1]-H[1])[0])==null?void 0:z[0];D&&(O=D);break}case"initialPlacement":O=a;break}if(r!==O)return{reset:{placement:O}}}return{}}}};function jn(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function Fn(e){return si.some(t=>e[t]>=0)}const hi=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:o="referenceHidden",...r}=ye(e,t);switch(o){case"referenceHidden":{const s=await et(t,{...r,elementContext:"reference"}),i=jn(s,n.reference);return{data:{referenceHiddenOffsets:i,referenceHidden:Fn(i)}}}case"escaped":{const s=await et(t,{...r,altBoundary:!0}),i=jn(s,n.floating);return{data:{escapedOffsets:i,escaped:Fn(i)}}}default:return{}}}}};async function vi(e,t){const{placement:n,platform:o,elements:r}=e,s=await(o.isRTL==null?void 0:o.isRTL(r.floating)),i=we(n),a=Ge(n),c=me(n)==="y",u=["left","top"].includes(i)?-1:1,f=s&&c?-1:1,p=ye(t,e);let{mainAxis:m,crossAxis:h,alignmentAxis:x}=typeof p=="number"?{mainAxis:p,crossAxis:0,alignmentAxis:null}:{mainAxis:p.mainAxis||0,crossAxis:p.crossAxis||0,alignmentAxis:p.alignmentAxis};return a&&typeof x=="number"&&(h=a==="end"?x*-1:x),c?{x:h*f,y:m*u}:{x:m*u,y:h*f}}const bi=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,o;const{x:r,y:s,placement:i,middlewareData:a}=t,c=await vi(t,e);return i===((n=a.offset)==null?void 0:n.placement)&&(o=a.arrow)!=null&&o.alignmentOffset?{}:{x:r+c.x,y:s+c.y,data:{...c,placement:i}}}}},xi=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:o,placement:r}=t,{mainAxis:s=!0,crossAxis:i=!1,limiter:a={fn:v=>{let{x:y,y:b}=v;return{x:y,y:b}}},...c}=ye(e,t),u={x:n,y:o},f=await et(t,c),p=me(we(r)),m=an(p);let h=u[m],x=u[p];if(s){const v=m==="y"?"top":"left",y=m==="y"?"bottom":"right",b=h+f[v],w=h-f[y];h=Yt(b,h,w)}if(i){const v=p==="y"?"top":"left",y=p==="y"?"bottom":"right",b=x+f[v],w=x-f[y];x=Yt(b,x,w)}const d=a.fn({...t,[m]:h,[p]:x});return{...d,data:{x:d.x-n,y:d.y-o,enabled:{[m]:s,[p]:i}}}}}},yi=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:o,placement:r,rects:s,middlewareData:i}=t,{offset:a=0,mainAxis:c=!0,crossAxis:u=!0}=ye(e,t),f={x:n,y:o},p=me(r),m=an(p);let h=f[m],x=f[p];const d=ye(a,t),v=typeof d=="number"?{mainAxis:d,crossAxis:0}:{mainAxis:0,crossAxis:0,...d};if(c){const w=m==="y"?"height":"width",C=s.reference[m]-s.floating[w]+v.mainAxis,R=s.reference[m]+s.reference[w]-v.mainAxis;h<C?h=C:h>R&&(h=R)}if(u){var y,b;const w=m==="y"?"width":"height",C=["top","left"].includes(we(r)),R=s.reference[p]-s.floating[w]+(C&&((y=i.offset)==null?void 0:y[p])||0)+(C?0:v.crossAxis),T=s.reference[p]+s.reference[w]+(C?0:((b=i.offset)==null?void 0:b[p])||0)-(C?v.crossAxis:0);x<R?x=R:x>T&&(x=T)}return{[m]:h,[p]:x}}}},wi=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,o;const{placement:r,rects:s,platform:i,elements:a}=t,{apply:c=()=>{},...u}=ye(e,t),f=await et(t,u),p=we(r),m=Ge(r),h=me(r)==="y",{width:x,height:d}=s.floating;let v,y;p==="top"||p==="bottom"?(v=p,y=m===(await(i.isRTL==null?void 0:i.isRTL(a.floating))?"start":"end")?"left":"right"):(y=p,v=m==="end"?"top":"bottom");const b=d-f.top-f.bottom,w=x-f.left-f.right,C=Ee(d-f[v],b),R=Ee(x-f[y],w),T=!t.middlewareData.shift;let P=C,E=R;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(E=w),(o=t.middlewareData.shift)!=null&&o.enabled.y&&(P=b),T&&!m){const L=oe(f.left,0),V=oe(f.right,0),B=oe(f.top,0),z=oe(f.bottom,0);h?E=x-2*(L!==0||V!==0?L+V:oe(f.left,f.right)):P=d-2*(B!==0||z!==0?B+z:oe(f.top,f.bottom))}await c({...t,availableWidth:E,availableHeight:P});const I=await i.getDimensions(a.floating);return x!==I.width||d!==I.height?{reset:{rects:!0}}:{}}}};function Ct(){return typeof window<"u"}function Ke(e){return vo(e)?(e.nodeName||"").toLowerCase():"#document"}function re(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function ve(e){var t;return(t=(vo(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function vo(e){return Ct()?e instanceof Node||e instanceof re(e).Node:!1}function ce(e){return Ct()?e instanceof Element||e instanceof re(e).Element:!1}function he(e){return Ct()?e instanceof HTMLElement||e instanceof re(e).HTMLElement:!1}function zn(e){return!Ct()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof re(e).ShadowRoot}function ot(e){const{overflow:t,overflowX:n,overflowY:o,display:r}=le(e);return/auto|scroll|overlay|hidden|clip/.test(t+o+n)&&!["inline","contents"].includes(r)}function Si(e){return["table","td","th"].includes(Ke(e))}function Et(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function un(e){const t=dn(),n=ce(e)?le(e):e;return["transform","translate","scale","rotate","perspective"].some(o=>n[o]?n[o]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(o=>(n.willChange||"").includes(o))||["paint","layout","strict","content"].some(o=>(n.contain||"").includes(o))}function Ci(e){let t=Re(e);for(;he(t)&&!Ve(t);){if(un(t))return t;if(Et(t))return null;t=Re(t)}return null}function dn(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function Ve(e){return["html","body","#document"].includes(Ke(e))}function le(e){return re(e).getComputedStyle(e)}function Rt(e){return ce(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Re(e){if(Ke(e)==="html")return e;const t=e.assignedSlot||e.parentNode||zn(e)&&e.host||ve(e);return zn(t)?t.host:t}function bo(e){const t=Re(e);return Ve(t)?e.ownerDocument?e.ownerDocument.body:e.body:he(t)&&ot(t)?t:bo(t)}function tt(e,t,n){var o;t===void 0&&(t=[]),n===void 0&&(n=!0);const r=bo(e),s=r===((o=e.ownerDocument)==null?void 0:o.body),i=re(r);if(s){const a=qt(i);return t.concat(i,i.visualViewport||[],ot(r)?r:[],a&&n?tt(a):[])}return t.concat(r,tt(r,[],n))}function qt(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function xo(e){const t=le(e);let n=parseFloat(t.width)||0,o=parseFloat(t.height)||0;const r=he(e),s=r?e.offsetWidth:n,i=r?e.offsetHeight:o,a=ht(n)!==s||ht(o)!==i;return a&&(n=s,o=i),{width:n,height:o,$:a}}function fn(e){return ce(e)?e:e.contextElement}function We(e){const t=fn(e);if(!he(t))return ge(1);const n=t.getBoundingClientRect(),{width:o,height:r,$:s}=xo(t);let i=(s?ht(n.width):n.width)/o,a=(s?ht(n.height):n.height)/r;return(!i||!Number.isFinite(i))&&(i=1),(!a||!Number.isFinite(a))&&(a=1),{x:i,y:a}}const Ei=ge(0);function yo(e){const t=re(e);return!dn()||!t.visualViewport?Ei:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function Ri(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==re(e)?!1:t}function Oe(e,t,n,o){t===void 0&&(t=!1),n===void 0&&(n=!1);const r=e.getBoundingClientRect(),s=fn(e);let i=ge(1);t&&(o?ce(o)&&(i=We(o)):i=We(e));const a=Ri(s,n,o)?yo(s):ge(0);let c=(r.left+a.x)/i.x,u=(r.top+a.y)/i.y,f=r.width/i.x,p=r.height/i.y;if(s){const m=re(s),h=o&&ce(o)?re(o):o;let x=m,d=qt(x);for(;d&&o&&h!==x;){const v=We(d),y=d.getBoundingClientRect(),b=le(d),w=y.left+(d.clientLeft+parseFloat(b.paddingLeft))*v.x,C=y.top+(d.clientTop+parseFloat(b.paddingTop))*v.y;c*=v.x,u*=v.y,f*=v.x,p*=v.y,c+=w,u+=C,x=re(d),d=qt(x)}}return bt({width:f,height:p,x:c,y:u})}function pn(e,t){const n=Rt(e).scrollLeft;return t?t.left+n:Oe(ve(e)).left+n}function wo(e,t,n){n===void 0&&(n=!1);const o=e.getBoundingClientRect(),r=o.left+t.scrollLeft-(n?0:pn(e,o)),s=o.top+t.scrollTop;return{x:r,y:s}}function Pi(e){let{elements:t,rect:n,offsetParent:o,strategy:r}=e;const s=r==="fixed",i=ve(o),a=t?Et(t.floating):!1;if(o===i||a&&s)return n;let c={scrollLeft:0,scrollTop:0},u=ge(1);const f=ge(0),p=he(o);if((p||!p&&!s)&&((Ke(o)!=="body"||ot(i))&&(c=Rt(o)),he(o))){const h=Oe(o);u=We(o),f.x=h.x+o.clientLeft,f.y=h.y+o.clientTop}const m=i&&!p&&!s?wo(i,c,!0):ge(0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-c.scrollLeft*u.x+f.x+m.x,y:n.y*u.y-c.scrollTop*u.y+f.y+m.y}}function Ai(e){return Array.from(e.getClientRects())}function ki(e){const t=ve(e),n=Rt(e),o=e.ownerDocument.body,r=oe(t.scrollWidth,t.clientWidth,o.scrollWidth,o.clientWidth),s=oe(t.scrollHeight,t.clientHeight,o.scrollHeight,o.clientHeight);let i=-n.scrollLeft+pn(e);const a=-n.scrollTop;return le(o).direction==="rtl"&&(i+=oe(t.clientWidth,o.clientWidth)-r),{width:r,height:s,x:i,y:a}}function Ni(e,t){const n=re(e),o=ve(e),r=n.visualViewport;let s=o.clientWidth,i=o.clientHeight,a=0,c=0;if(r){s=r.width,i=r.height;const u=dn();(!u||u&&t==="fixed")&&(a=r.offsetLeft,c=r.offsetTop)}return{width:s,height:i,x:a,y:c}}function Ti(e,t){const n=Oe(e,!0,t==="fixed"),o=n.top+e.clientTop,r=n.left+e.clientLeft,s=he(e)?We(e):ge(1),i=e.clientWidth*s.x,a=e.clientHeight*s.y,c=r*s.x,u=o*s.y;return{width:i,height:a,x:c,y:u}}function Wn(e,t,n){let o;if(t==="viewport")o=Ni(e,n);else if(t==="document")o=ki(ve(e));else if(ce(t))o=Ti(t,n);else{const r=yo(e);o={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return bt(o)}function So(e,t){const n=Re(e);return n===t||!ce(n)||Ve(n)?!1:le(n).position==="fixed"||So(n,t)}function Oi(e,t){const n=t.get(e);if(n)return n;let o=tt(e,[],!1).filter(a=>ce(a)&&Ke(a)!=="body"),r=null;const s=le(e).position==="fixed";let i=s?Re(e):e;for(;ce(i)&&!Ve(i);){const a=le(i),c=un(i);!c&&a.position==="fixed"&&(r=null),(s?!c&&!r:!c&&a.position==="static"&&!!r&&["absolute","fixed"].includes(r.position)||ot(i)&&!c&&So(e,i))?o=o.filter(f=>f!==i):r=a,i=Re(i)}return t.set(e,o),o}function Ii(e){let{element:t,boundary:n,rootBoundary:o,strategy:r}=e;const i=[...n==="clippingAncestors"?Et(t)?[]:Oi(t,this._c):[].concat(n),o],a=i[0],c=i.reduce((u,f)=>{const p=Wn(t,f,r);return u.top=oe(p.top,u.top),u.right=Ee(p.right,u.right),u.bottom=Ee(p.bottom,u.bottom),u.left=oe(p.left,u.left),u},Wn(t,a,r));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}}function Mi(e){const{width:t,height:n}=xo(e);return{width:t,height:n}}function Di(e,t,n){const o=he(t),r=ve(t),s=n==="fixed",i=Oe(e,!0,s,t);let a={scrollLeft:0,scrollTop:0};const c=ge(0);function u(){c.x=pn(r)}if(o||!o&&!s)if((Ke(t)!=="body"||ot(r))&&(a=Rt(t)),o){const h=Oe(t,!0,s,t);c.x=h.x+t.clientLeft,c.y=h.y+t.clientTop}else r&&u();s&&!o&&r&&u();const f=r&&!o&&!s?wo(r,a):ge(0),p=i.left+a.scrollLeft-c.x-f.x,m=i.top+a.scrollTop-c.y-f.y;return{x:p,y:m,width:i.width,height:i.height}}function jt(e){return le(e).position==="static"}function Bn(e,t){if(!he(e)||le(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return ve(e)===n&&(n=n.ownerDocument.body),n}function Co(e,t){const n=re(e);if(Et(e))return n;if(!he(e)){let r=Re(e);for(;r&&!Ve(r);){if(ce(r)&&!jt(r))return r;r=Re(r)}return n}let o=Bn(e,t);for(;o&&Si(o)&&jt(o);)o=Bn(o,t);return o&&Ve(o)&&jt(o)&&!un(o)?n:o||Ci(e)||n}const _i=async function(e){const t=this.getOffsetParent||Co,n=this.getDimensions,o=await n(e.floating);return{reference:Di(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:o.width,height:o.height}}};function Li(e){return le(e).direction==="rtl"}const ji={convertOffsetParentRelativeRectToViewportRelativeRect:Pi,getDocumentElement:ve,getClippingRect:Ii,getOffsetParent:Co,getElementRects:_i,getClientRects:Ai,getDimensions:Mi,getScale:We,isElement:ce,isRTL:Li};function Eo(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function Fi(e,t){let n=null,o;const r=ve(e);function s(){var a;clearTimeout(o),(a=n)==null||a.disconnect(),n=null}function i(a,c){a===void 0&&(a=!1),c===void 0&&(c=1),s();const u=e.getBoundingClientRect(),{left:f,top:p,width:m,height:h}=u;if(a||t(),!m||!h)return;const x=at(p),d=at(r.clientWidth-(f+m)),v=at(r.clientHeight-(p+h)),y=at(f),w={rootMargin:-x+"px "+-d+"px "+-v+"px "+-y+"px",threshold:oe(0,Ee(1,c))||1};let C=!0;function R(T){const P=T[0].intersectionRatio;if(P!==c){if(!C)return i();P?i(!1,P):o=setTimeout(()=>{i(!1,1e-7)},1e3)}P===1&&!Eo(u,e.getBoundingClientRect())&&i(),C=!1}try{n=new IntersectionObserver(R,{...w,root:r.ownerDocument})}catch{n=new IntersectionObserver(R,w)}n.observe(e)}return i(!0),s}function zi(e,t,n,o){o===void 0&&(o={});const{ancestorScroll:r=!0,ancestorResize:s=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:c=!1}=o,u=fn(e),f=r||s?[...u?tt(u):[],...tt(t)]:[];f.forEach(y=>{r&&y.addEventListener("scroll",n,{passive:!0}),s&&y.addEventListener("resize",n)});const p=u&&a?Fi(u,n):null;let m=-1,h=null;i&&(h=new ResizeObserver(y=>{let[b]=y;b&&b.target===u&&h&&(h.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var w;(w=h)==null||w.observe(t)})),n()}),u&&!c&&h.observe(u),h.observe(t));let x,d=c?Oe(e):null;c&&v();function v(){const y=Oe(e);d&&!Eo(d,y)&&n(),d=y,x=requestAnimationFrame(v)}return n(),()=>{var y;f.forEach(b=>{r&&b.removeEventListener("scroll",n),s&&b.removeEventListener("resize",n)}),p==null||p(),(y=h)==null||y.disconnect(),h=null,c&&cancelAnimationFrame(x)}}const Wi=bi,Bi=xi,$i=gi,Vi=wi,Hi=hi,$n=mi,Ui=yi,Gi=(e,t,n)=>{const o=new Map,r={platform:ji,...n},s={...r.platform,_c:o};return pi(e,t,{...r,platform:s})};var Ki=typeof document<"u",Yi=function(){},pt=Ki?l.useLayoutEffect:Yi;function xt(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,o,r;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(o=n;o--!==0;)if(!xt(e[o],t[o]))return!1;return!0}if(r=Object.keys(e),n=r.length,n!==Object.keys(t).length)return!1;for(o=n;o--!==0;)if(!{}.hasOwnProperty.call(t,r[o]))return!1;for(o=n;o--!==0;){const s=r[o];if(!(s==="_owner"&&e.$$typeof)&&!xt(e[s],t[s]))return!1}return!0}return e!==e&&t!==t}function Ro(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Vn(e,t){const n=Ro(e);return Math.round(t*n)/n}function Ft(e){const t=l.useRef(e);return pt(()=>{t.current=e}),t}function Xi(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:r,elements:{reference:s,floating:i}={},transform:a=!0,whileElementsMounted:c,open:u}=e,[f,p]=l.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[m,h]=l.useState(o);xt(m,o)||h(o);const[x,d]=l.useState(null),[v,y]=l.useState(null),b=l.useCallback(S=>{S!==T.current&&(T.current=S,d(S))},[]),w=l.useCallback(S=>{S!==P.current&&(P.current=S,y(S))},[]),C=s||x,R=i||v,T=l.useRef(null),P=l.useRef(null),E=l.useRef(f),I=c!=null,L=Ft(c),V=Ft(r),B=Ft(u),z=l.useCallback(()=>{if(!T.current||!P.current)return;const S={placement:t,strategy:n,middleware:m};V.current&&(S.platform=V.current),Gi(T.current,P.current,S).then(H=>{const ee={...H,isPositioned:B.current!==!1};_.current&&!xt(E.current,ee)&&(E.current=ee,nt.flushSync(()=>{p(ee)}))})},[m,t,n,V,B]);pt(()=>{u===!1&&E.current.isPositioned&&(E.current.isPositioned=!1,p(S=>({...S,isPositioned:!1})))},[u]);const _=l.useRef(!1);pt(()=>(_.current=!0,()=>{_.current=!1}),[]),pt(()=>{if(C&&(T.current=C),R&&(P.current=R),C&&R){if(L.current)return L.current(C,R,z);z()}},[C,R,z,L,I]);const $=l.useMemo(()=>({reference:T,floating:P,setReference:b,setFloating:w}),[b,w]),O=l.useMemo(()=>({reference:C,floating:R}),[C,R]),D=l.useMemo(()=>{const S={position:n,left:0,top:0};if(!O.floating)return S;const H=Vn(O.floating,f.x),ee=Vn(O.floating,f.y);return a?{...S,transform:"translate("+H+"px, "+ee+"px)",...Ro(O.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:H,top:ee}},[n,a,O.floating,f.x,f.y]);return l.useMemo(()=>({...f,update:z,refs:$,elements:O,floatingStyles:D}),[f,z,$,O,D])}const qi=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:o,padding:r}=typeof e=="function"?e(n):e;return o&&t(o)?o.current!=null?$n({element:o.current,padding:r}).fn(n):{}:o?$n({element:o,padding:r}).fn(n):{}}}},Zi=(e,t)=>({...Wi(e),options:[e,t]}),Qi=(e,t)=>({...Bi(e),options:[e,t]}),Ji=(e,t)=>({...Ui(e),options:[e,t]}),ea=(e,t)=>({...$i(e),options:[e,t]}),ta=(e,t)=>({...Vi(e),options:[e,t]}),na=(e,t)=>({...Hi(e),options:[e,t]}),oa=(e,t)=>({...qi(e),options:[e,t]});var ra="Arrow",Po=l.forwardRef((e,t)=>{const{children:n,width:o=10,height:r=5,...s}=e;return g.jsx(G.svg,{...s,ref:t,width:o,height:r,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:g.jsx("polygon",{points:"0,0 30,0 15,10"})})});Po.displayName=ra;var sa=Po;function ia(e){const[t,n]=l.useState(void 0);return te(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const o=new ResizeObserver(r=>{if(!Array.isArray(r)||!r.length)return;const s=r[0];let i,a;if("borderBoxSize"in s){const c=s.borderBoxSize,u=Array.isArray(c)?c[0]:c;i=u.inlineSize,a=u.blockSize}else i=e.offsetWidth,a=e.offsetHeight;n({width:i,height:a})});return o.observe(e,{box:"border-box"}),()=>o.unobserve(e)}else n(void 0)},[e]),t}var mn="Popper",[Ao,ko]=St(mn),[aa,No]=Ao(mn),To=e=>{const{__scopePopper:t,children:n}=e,[o,r]=l.useState(null);return g.jsx(aa,{scope:t,anchor:o,onAnchorChange:r,children:n})};To.displayName=mn;var Oo="PopperAnchor",Io=l.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:o,...r}=e,s=No(Oo,n),i=l.useRef(null),a=Q(t,i);return l.useEffect(()=>{s.onAnchorChange((o==null?void 0:o.current)||i.current)}),o?null:g.jsx(G.div,{...r,ref:a})});Io.displayName=Oo;var gn="PopperContent",[ca,la]=Ao(gn),Mo=l.forwardRef((e,t)=>{var N,U,Z,W,F,j;const{__scopePopper:n,side:o="bottom",sideOffset:r=0,align:s="center",alignOffset:i=0,arrowPadding:a=0,avoidCollisions:c=!0,collisionBoundary:u=[],collisionPadding:f=0,sticky:p="partial",hideWhenDetached:m=!1,updatePositionStrategy:h="optimized",onPlaced:x,...d}=e,v=No(gn,n),[y,b]=l.useState(null),w=Q(t,ne=>b(ne)),[C,R]=l.useState(null),T=ia(C),P=(T==null?void 0:T.width)??0,E=(T==null?void 0:T.height)??0,I=o+(s!=="center"?"-"+s:""),L=typeof f=="number"?f:{top:0,right:0,bottom:0,left:0,...f},V=Array.isArray(u)?u:[u],B=V.length>0,z={padding:L,boundary:V.filter(da),altBoundary:B},{refs:_,floatingStyles:$,placement:O,isPositioned:D,middlewareData:S}=Xi({strategy:"fixed",placement:I,whileElementsMounted:(...ne)=>zi(...ne,{animationFrame:h==="always"}),elements:{reference:v.anchor},middleware:[Zi({mainAxis:r+E,alignmentAxis:i}),c&&Qi({mainAxis:!0,crossAxis:!1,limiter:p==="partial"?Ji():void 0,...z}),c&&ea({...z}),ta({...z,apply:({elements:ne,rects:fe,availableWidth:Xe,availableHeight:qe})=>{const{width:Ze,height:jr}=fe.reference,rt=ne.floating.style;rt.setProperty("--radix-popper-available-width",`${Xe}px`),rt.setProperty("--radix-popper-available-height",`${qe}px`),rt.setProperty("--radix-popper-anchor-width",`${Ze}px`),rt.setProperty("--radix-popper-anchor-height",`${jr}px`)}}),C&&oa({element:C,padding:a}),fa({arrowWidth:P,arrowHeight:E}),m&&na({strategy:"referenceHidden",...z})]}),[H,ee]=Lo(O),se=Te(x);te(()=>{D&&(se==null||se())},[D,se]);const be=(N=S.arrow)==null?void 0:N.x,q=(U=S.arrow)==null?void 0:U.y,X=((Z=S.arrow)==null?void 0:Z.centerOffset)!==0,[ie,de]=l.useState();return te(()=>{y&&de(window.getComputedStyle(y).zIndex)},[y]),g.jsx("div",{ref:_.setFloating,"data-radix-popper-content-wrapper":"",style:{...$,transform:D?$.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ie,"--radix-popper-transform-origin":[(W=S.transformOrigin)==null?void 0:W.x,(F=S.transformOrigin)==null?void 0:F.y].join(" "),...((j=S.hide)==null?void 0:j.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:g.jsx(ca,{scope:n,placedSide:H,onArrowChange:R,arrowX:be,arrowY:q,shouldHideArrow:X,children:g.jsx(G.div,{"data-side":H,"data-align":ee,...d,ref:w,style:{...d.style,animation:D?void 0:"none"}})})})});Mo.displayName=gn;var Do="PopperArrow",ua={top:"bottom",right:"left",bottom:"top",left:"right"},_o=l.forwardRef(function(t,n){const{__scopePopper:o,...r}=t,s=la(Do,o),i=ua[s.placedSide];return g.jsx("span",{ref:s.onArrowChange,style:{position:"absolute",left:s.arrowX,top:s.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[s.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[s.placedSide],visibility:s.shouldHideArrow?"hidden":void 0},children:g.jsx(sa,{...r,ref:n,style:{...r.style,display:"block"}})})});_o.displayName=Do;function da(e){return e!==null}var fa=e=>({name:"transformOrigin",options:e,fn(t){var v,y,b;const{placement:n,rects:o,middlewareData:r}=t,i=((v=r.arrow)==null?void 0:v.centerOffset)!==0,a=i?0:e.arrowWidth,c=i?0:e.arrowHeight,[u,f]=Lo(n),p={start:"0%",center:"50%",end:"100%"}[f],m=(((y=r.arrow)==null?void 0:y.x)??0)+a/2,h=(((b=r.arrow)==null?void 0:b.y)??0)+c/2;let x="",d="";return u==="bottom"?(x=i?p:`${m}px`,d=`${-c}px`):u==="top"?(x=i?p:`${m}px`,d=`${o.floating.height+c}px`):u==="right"?(x=`${-c}px`,d=i?p:`${h}px`):u==="left"&&(x=`${o.floating.width+c}px`,d=i?p:`${h}px`),{data:{x,y:d}}}});function Lo(e){const[t,n="center"]=e.split("-");return[t,n]}var pa=To,ma=Io,ga=Mo,ha=_o,va="Portal",hn=l.forwardRef((e,t)=>{var a;const{container:n,...o}=e,[r,s]=l.useState(!1);te(()=>s(!0),[]);const i=n||r&&((a=globalThis==null?void 0:globalThis.document)==null?void 0:a.body);return i?Ls.createPortal(g.jsx(G.div,{...o,ref:t}),i):null});hn.displayName=va;function ba(e,t){return l.useReducer((n,o)=>t[n][o]??n,e)}var Pt=e=>{const{present:t,children:n}=e,o=xa(t),r=typeof n=="function"?n({present:o.isPresent}):l.Children.only(n),s=Q(o.ref,ya(r));return typeof n=="function"||o.isPresent?l.cloneElement(r,{ref:s}):null};Pt.displayName="Presence";function xa(e){const[t,n]=l.useState(),o=l.useRef(null),r=l.useRef(e),s=l.useRef("none"),i=e?"mounted":"unmounted",[a,c]=ba(i,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return l.useEffect(()=>{const u=ct(o.current);s.current=a==="mounted"?u:"none"},[a]),te(()=>{const u=o.current,f=r.current;if(f!==e){const m=s.current,h=ct(u);e?c("MOUNT"):h==="none"||(u==null?void 0:u.display)==="none"?c("UNMOUNT"):c(f&&m!==h?"ANIMATION_OUT":"UNMOUNT"),r.current=e}},[e,c]),te(()=>{if(t){let u;const f=t.ownerDocument.defaultView??window,p=h=>{const d=ct(o.current).includes(h.animationName);if(h.target===t&&d&&(c("ANIMATION_END"),!r.current)){const v=t.style.animationFillMode;t.style.animationFillMode="forwards",u=f.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=v)})}},m=h=>{h.target===t&&(s.current=ct(o.current))};return t.addEventListener("animationstart",m),t.addEventListener("animationcancel",p),t.addEventListener("animationend",p),()=>{f.clearTimeout(u),t.removeEventListener("animationstart",m),t.removeEventListener("animationcancel",p),t.removeEventListener("animationend",p)}}else c("ANIMATION_END")},[t,c]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:l.useCallback(u=>{o.current=u?getComputedStyle(u):null,n(u)},[])}}function ct(e){return(e==null?void 0:e.animationName)||"none"}function ya(e){var o,r;let t=(o=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(r=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var wa=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},Le=new WeakMap,lt=new WeakMap,ut={},zt=0,jo=function(e){return e&&(e.host||jo(e.parentNode))},Sa=function(e,t){return t.map(function(n){if(e.contains(n))return n;var o=jo(n);return o&&e.contains(o)?o:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},Ca=function(e,t,n,o){var r=Sa(t,Array.isArray(e)?e:[e]);ut[n]||(ut[n]=new WeakMap);var s=ut[n],i=[],a=new Set,c=new Set(r),u=function(p){!p||a.has(p)||(a.add(p),u(p.parentNode))};r.forEach(u);var f=function(p){!p||c.has(p)||Array.prototype.forEach.call(p.children,function(m){if(a.has(m))f(m);else try{var h=m.getAttribute(o),x=h!==null&&h!=="false",d=(Le.get(m)||0)+1,v=(s.get(m)||0)+1;Le.set(m,d),s.set(m,v),i.push(m),d===1&&x&&lt.set(m,!0),v===1&&m.setAttribute(n,"true"),x||m.setAttribute(o,"true")}catch(y){console.error("aria-hidden: cannot operate on ",m,y)}})};return f(t),a.clear(),zt++,function(){i.forEach(function(p){var m=Le.get(p)-1,h=s.get(p)-1;Le.set(p,m),s.set(p,h),m||(lt.has(p)||p.removeAttribute(o),lt.delete(p)),h||p.removeAttribute(n)}),zt--,zt||(Le=new WeakMap,Le=new WeakMap,lt=new WeakMap,ut={})}},Fo=function(e,t,n){n===void 0&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),r=wa(e);return r?(o.push.apply(o,Array.from(r.querySelectorAll("[aria-live], script"))),Ca(o,r,n,"aria-hidden")):function(){return null}},pe=function(){return pe=Object.assign||function(t){for(var n,o=1,r=arguments.length;o<r;o++){n=arguments[o];for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&(t[s]=n[s])}return t},pe.apply(this,arguments)};function zo(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n}function Ea(e,t,n){if(n||arguments.length===2)for(var o=0,r=t.length,s;o<r;o++)(s||!(o in t))&&(s||(s=Array.prototype.slice.call(t,0,o)),s[o]=t[o]);return e.concat(s||Array.prototype.slice.call(t))}var mt="right-scroll-bar-position",gt="width-before-scroll-bar",Ra="with-scroll-bars-hidden",Pa="--removed-body-scroll-bar-size";function Wt(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function Aa(e,t){var n=l.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(o){var r=n.value;r!==o&&(n.value=o,n.callback(o,r))}}}})[0];return n.callback=t,n.facade}var ka=typeof window<"u"?l.useLayoutEffect:l.useEffect,Hn=new WeakMap;function Na(e,t){var n=Aa(null,function(o){return e.forEach(function(r){return Wt(r,o)})});return ka(function(){var o=Hn.get(n);if(o){var r=new Set(o),s=new Set(e),i=n.current;r.forEach(function(a){s.has(a)||Wt(a,null)}),s.forEach(function(a){r.has(a)||Wt(a,i)})}Hn.set(n,e)},[e]),n}function Ta(e){return e}function Oa(e,t){t===void 0&&(t=Ta);var n=[],o=!1,r={read:function(){if(o)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(s){var i=t(s,o);return n.push(i),function(){n=n.filter(function(a){return a!==i})}},assignSyncMedium:function(s){for(o=!0;n.length;){var i=n;n=[],i.forEach(s)}n={push:function(a){return s(a)},filter:function(){return n}}},assignMedium:function(s){o=!0;var i=[];if(n.length){var a=n;n=[],a.forEach(s),i=n}var c=function(){var f=i;i=[],f.forEach(s)},u=function(){return Promise.resolve().then(c)};u(),n={push:function(f){i.push(f),u()},filter:function(f){return i=i.filter(f),n}}}};return r}function Ia(e){e===void 0&&(e={});var t=Oa(null);return t.options=pe({async:!0,ssr:!1},e),t}var Wo=function(e){var t=e.sideCar,n=zo(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var o=t.read();if(!o)throw new Error("Sidecar medium not found");return l.createElement(o,pe({},n))};Wo.isSideCarExport=!0;function Ma(e,t){return e.useMedium(t),Wo}var Bo=Ia(),Bt=function(){},At=l.forwardRef(function(e,t){var n=l.useRef(null),o=l.useState({onScrollCapture:Bt,onWheelCapture:Bt,onTouchMoveCapture:Bt}),r=o[0],s=o[1],i=e.forwardProps,a=e.children,c=e.className,u=e.removeScrollBar,f=e.enabled,p=e.shards,m=e.sideCar,h=e.noRelative,x=e.noIsolation,d=e.inert,v=e.allowPinchZoom,y=e.as,b=y===void 0?"div":y,w=e.gapMode,C=zo(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),R=m,T=Na([n,t]),P=pe(pe({},C),r);return l.createElement(l.Fragment,null,f&&l.createElement(R,{sideCar:Bo,removeScrollBar:u,shards:p,noRelative:h,noIsolation:x,inert:d,setCallbacks:s,allowPinchZoom:!!v,lockRef:n,gapMode:w}),i?l.cloneElement(l.Children.only(a),pe(pe({},P),{ref:T})):l.createElement(b,pe({},P,{className:c,ref:T}),a))});At.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};At.classNames={fullWidth:gt,zeroRight:mt};var Da=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function _a(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=Da();return t&&e.setAttribute("nonce",t),e}function La(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function ja(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var Fa=function(){var e=0,t=null;return{add:function(n){e==0&&(t=_a())&&(La(t,n),ja(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},za=function(){var e=Fa();return function(t,n){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},$o=function(){var e=za(),t=function(n){var o=n.styles,r=n.dynamic;return e(o,r),null};return t},Wa={left:0,top:0,right:0,gap:0},$t=function(e){return parseInt(e||"",10)||0},Ba=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],o=t[e==="padding"?"paddingTop":"marginTop"],r=t[e==="padding"?"paddingRight":"marginRight"];return[$t(n),$t(o),$t(r)]},$a=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return Wa;var t=Ba(e),n=document.documentElement.clientWidth,o=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,o-n+t[2]-t[0])}},Va=$o(),Be="data-scroll-locked",Ha=function(e,t,n,o){var r=e.left,s=e.top,i=e.right,a=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(Ra,` {
   overflow: hidden `).concat(o,`;
   padding-right: `).concat(a,"px ").concat(o,`;
  }
  body[`).concat(Be,`] {
    overflow: hidden `).concat(o,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(o,";"),n==="margin"&&`
    padding-left: `.concat(r,`px;
    padding-top: `).concat(s,`px;
    padding-right: `).concat(i,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(a,"px ").concat(o,`;
    `),n==="padding"&&"padding-right: ".concat(a,"px ").concat(o,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(mt,` {
    right: `).concat(a,"px ").concat(o,`;
  }
  
  .`).concat(gt,` {
    margin-right: `).concat(a,"px ").concat(o,`;
  }
  
  .`).concat(mt," .").concat(mt,` {
    right: 0 `).concat(o,`;
  }
  
  .`).concat(gt," .").concat(gt,` {
    margin-right: 0 `).concat(o,`;
  }
  
  body[`).concat(Be,`] {
    `).concat(Pa,": ").concat(a,`px;
  }
`)},Un=function(){var e=parseInt(document.body.getAttribute(Be)||"0",10);return isFinite(e)?e:0},Ua=function(){l.useEffect(function(){return document.body.setAttribute(Be,(Un()+1).toString()),function(){var e=Un()-1;e<=0?document.body.removeAttribute(Be):document.body.setAttribute(Be,e.toString())}},[])},Ga=function(e){var t=e.noRelative,n=e.noImportant,o=e.gapMode,r=o===void 0?"margin":o;Ua();var s=l.useMemo(function(){return $a(r)},[r]);return l.createElement(Va,{styles:Ha(s,!t,r,n?"":"!important")})},Zt=!1;if(typeof window<"u")try{var dt=Object.defineProperty({},"passive",{get:function(){return Zt=!0,!0}});window.addEventListener("test",dt,dt),window.removeEventListener("test",dt,dt)}catch{Zt=!1}var je=Zt?{passive:!1}:!1,Ka=function(e){return e.tagName==="TEXTAREA"},Vo=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!Ka(e)&&n[t]==="visible")},Ya=function(e){return Vo(e,"overflowY")},Xa=function(e){return Vo(e,"overflowX")},Gn=function(e,t){var n=t.ownerDocument,o=t;do{typeof ShadowRoot<"u"&&o instanceof ShadowRoot&&(o=o.host);var r=Ho(e,o);if(r){var s=Uo(e,o),i=s[1],a=s[2];if(i>a)return!0}o=o.parentNode}while(o&&o!==n.body);return!1},qa=function(e){var t=e.scrollTop,n=e.scrollHeight,o=e.clientHeight;return[t,n,o]},Za=function(e){var t=e.scrollLeft,n=e.scrollWidth,o=e.clientWidth;return[t,n,o]},Ho=function(e,t){return e==="v"?Ya(t):Xa(t)},Uo=function(e,t){return e==="v"?qa(t):Za(t)},Qa=function(e,t){return e==="h"&&t==="rtl"?-1:1},Ja=function(e,t,n,o,r){var s=Qa(e,window.getComputedStyle(t).direction),i=s*o,a=n.target,c=t.contains(a),u=!1,f=i>0,p=0,m=0;do{if(!a)break;var h=Uo(e,a),x=h[0],d=h[1],v=h[2],y=d-v-s*x;(x||y)&&Ho(e,a)&&(p+=y,m+=x);var b=a.parentNode;a=b&&b.nodeType===Node.DOCUMENT_FRAGMENT_NODE?b.host:b}while(!c&&a!==document.body||c&&(t.contains(a)||t===a));return(f&&Math.abs(p)<1||!f&&Math.abs(m)<1)&&(u=!0),u},ft=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Kn=function(e){return[e.deltaX,e.deltaY]},Yn=function(e){return e&&"current"in e?e.current:e},ec=function(e,t){return e[0]===t[0]&&e[1]===t[1]},tc=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},nc=0,Fe=[];function oc(e){var t=l.useRef([]),n=l.useRef([0,0]),o=l.useRef(),r=l.useState(nc++)[0],s=l.useState($o)[0],i=l.useRef(e);l.useEffect(function(){i.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(r));var d=Ea([e.lockRef.current],(e.shards||[]).map(Yn),!0).filter(Boolean);return d.forEach(function(v){return v.classList.add("allow-interactivity-".concat(r))}),function(){document.body.classList.remove("block-interactivity-".concat(r)),d.forEach(function(v){return v.classList.remove("allow-interactivity-".concat(r))})}}},[e.inert,e.lockRef.current,e.shards]);var a=l.useCallback(function(d,v){if("touches"in d&&d.touches.length===2||d.type==="wheel"&&d.ctrlKey)return!i.current.allowPinchZoom;var y=ft(d),b=n.current,w="deltaX"in d?d.deltaX:b[0]-y[0],C="deltaY"in d?d.deltaY:b[1]-y[1],R,T=d.target,P=Math.abs(w)>Math.abs(C)?"h":"v";if("touches"in d&&P==="h"&&T.type==="range")return!1;var E=Gn(P,T);if(!E)return!0;if(E?R=P:(R=P==="v"?"h":"v",E=Gn(P,T)),!E)return!1;if(!o.current&&"changedTouches"in d&&(w||C)&&(o.current=R),!R)return!0;var I=o.current||R;return Ja(I,v,d,I==="h"?w:C)},[]),c=l.useCallback(function(d){var v=d;if(!(!Fe.length||Fe[Fe.length-1]!==s)){var y="deltaY"in v?Kn(v):ft(v),b=t.current.filter(function(R){return R.name===v.type&&(R.target===v.target||v.target===R.shadowParent)&&ec(R.delta,y)})[0];if(b&&b.should){v.cancelable&&v.preventDefault();return}if(!b){var w=(i.current.shards||[]).map(Yn).filter(Boolean).filter(function(R){return R.contains(v.target)}),C=w.length>0?a(v,w[0]):!i.current.noIsolation;C&&v.cancelable&&v.preventDefault()}}},[]),u=l.useCallback(function(d,v,y,b){var w={name:d,delta:v,target:y,should:b,shadowParent:rc(y)};t.current.push(w),setTimeout(function(){t.current=t.current.filter(function(C){return C!==w})},1)},[]),f=l.useCallback(function(d){n.current=ft(d),o.current=void 0},[]),p=l.useCallback(function(d){u(d.type,Kn(d),d.target,a(d,e.lockRef.current))},[]),m=l.useCallback(function(d){u(d.type,ft(d),d.target,a(d,e.lockRef.current))},[]);l.useEffect(function(){return Fe.push(s),e.setCallbacks({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:m}),document.addEventListener("wheel",c,je),document.addEventListener("touchmove",c,je),document.addEventListener("touchstart",f,je),function(){Fe=Fe.filter(function(d){return d!==s}),document.removeEventListener("wheel",c,je),document.removeEventListener("touchmove",c,je),document.removeEventListener("touchstart",f,je)}},[]);var h=e.removeScrollBar,x=e.inert;return l.createElement(l.Fragment,null,x?l.createElement(s,{styles:tc(r)}):null,h?l.createElement(Ga,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}function rc(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const sc=Ma(Bo,oc);var vn=l.forwardRef(function(e,t){return l.createElement(At,pe({},e,{ref:t,sideCar:sc}))});vn.classNames=At.classNames;var kt="Dialog",[Go,hl]=St(kt),[ic,ue]=Go(kt),Ko=e=>{const{__scopeDialog:t,children:n,open:o,defaultOpen:r,onOpenChange:s,modal:i=!0}=e,a=l.useRef(null),c=l.useRef(null),[u,f]=Gt({prop:o,defaultProp:r??!1,onChange:s,caller:kt});return g.jsx(ic,{scope:t,triggerRef:a,contentRef:c,contentId:ze(),titleId:ze(),descriptionId:ze(),open:u,onOpenChange:f,onOpenToggle:l.useCallback(()=>f(p=>!p),[f]),modal:i,children:n})};Ko.displayName=kt;var Yo="DialogTrigger",Xo=l.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=ue(Yo,n),s=Q(t,r.triggerRef);return g.jsx(G.button,{type:"button","aria-haspopup":"dialog","aria-expanded":r.open,"aria-controls":r.contentId,"data-state":yn(r.open),...o,ref:s,onClick:K(e.onClick,r.onOpenToggle)})});Xo.displayName=Yo;var bn="DialogPortal",[ac,qo]=Go(bn,{forceMount:void 0}),Zo=e=>{const{__scopeDialog:t,forceMount:n,children:o,container:r}=e,s=ue(bn,t);return g.jsx(ac,{scope:t,forceMount:n,children:l.Children.map(o,i=>g.jsx(Pt,{present:n||s.open,children:g.jsx(hn,{asChild:!0,container:r,children:i})}))})};Zo.displayName=bn;var yt="DialogOverlay",Qo=l.forwardRef((e,t)=>{const n=qo(yt,e.__scopeDialog),{forceMount:o=n.forceMount,...r}=e,s=ue(yt,e.__scopeDialog);return s.modal?g.jsx(Pt,{present:o||s.open,children:g.jsx(lc,{...r,ref:t})}):null});Qo.displayName=yt;var cc=$e("DialogOverlay.RemoveScroll"),lc=l.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=ue(yt,n);return g.jsx(vn,{as:cc,allowPinchZoom:!0,shards:[r.contentRef],children:g.jsx(G.div,{"data-state":yn(r.open),...o,ref:t,style:{pointerEvents:"auto",...o.style}})})}),Ie="DialogContent",Jo=l.forwardRef((e,t)=>{const n=qo(Ie,e.__scopeDialog),{forceMount:o=n.forceMount,...r}=e,s=ue(Ie,e.__scopeDialog);return g.jsx(Pt,{present:o||s.open,children:s.modal?g.jsx(uc,{...r,ref:t}):g.jsx(dc,{...r,ref:t})})});Jo.displayName=Ie;var uc=l.forwardRef((e,t)=>{const n=ue(Ie,e.__scopeDialog),o=l.useRef(null),r=Q(t,n.contentRef,o);return l.useEffect(()=>{const s=o.current;if(s)return Fo(s)},[]),g.jsx(er,{...e,ref:r,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:K(e.onCloseAutoFocus,s=>{var i;s.preventDefault(),(i=n.triggerRef.current)==null||i.focus()}),onPointerDownOutside:K(e.onPointerDownOutside,s=>{const i=s.detail.originalEvent,a=i.button===0&&i.ctrlKey===!0;(i.button===2||a)&&s.preventDefault()}),onFocusOutside:K(e.onFocusOutside,s=>s.preventDefault())})}),dc=l.forwardRef((e,t)=>{const n=ue(Ie,e.__scopeDialog),o=l.useRef(!1),r=l.useRef(!1);return g.jsx(er,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:s=>{var i,a;(i=e.onCloseAutoFocus)==null||i.call(e,s),s.defaultPrevented||(o.current||(a=n.triggerRef.current)==null||a.focus(),s.preventDefault()),o.current=!1,r.current=!1},onInteractOutside:s=>{var c,u;(c=e.onInteractOutside)==null||c.call(e,s),s.defaultPrevented||(o.current=!0,s.detail.originalEvent.type==="pointerdown"&&(r.current=!0));const i=s.target;((u=n.triggerRef.current)==null?void 0:u.contains(i))&&s.preventDefault(),s.detail.originalEvent.type==="focusin"&&r.current&&s.preventDefault()}})}),er=l.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:r,onCloseAutoFocus:s,...i}=e,a=ue(Ie,n),c=l.useRef(null),u=Q(t,c);return mo(),g.jsxs(g.Fragment,{children:[g.jsx(sn,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:r,onUnmountAutoFocus:s,children:g.jsx(rn,{role:"dialog",id:a.contentId,"aria-describedby":a.descriptionId,"aria-labelledby":a.titleId,"data-state":yn(a.open),...i,ref:u,onDismiss:()=>a.onOpenChange(!1)})}),g.jsxs(g.Fragment,{children:[g.jsx(fc,{titleId:a.titleId}),g.jsx(mc,{contentRef:c,descriptionId:a.descriptionId})]})]})}),xn="DialogTitle",tr=l.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=ue(xn,n);return g.jsx(G.h2,{id:r.titleId,...o,ref:t})});tr.displayName=xn;var nr="DialogDescription",or=l.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=ue(nr,n);return g.jsx(G.p,{id:r.descriptionId,...o,ref:t})});or.displayName=nr;var rr="DialogClose",sr=l.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=ue(rr,n);return g.jsx(G.button,{type:"button",...o,ref:t,onClick:K(e.onClick,()=>r.onOpenChange(!1))})});sr.displayName=rr;function yn(e){return e?"open":"closed"}var ir="DialogTitleWarning",[vl,ar]=Os(ir,{contentName:Ie,titleName:xn,docsSlug:"dialog"}),fc=({titleId:e})=>{const t=ar(ir),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return l.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},pc="DialogDescriptionWarning",mc=({contentRef:e,descriptionId:t})=>{const o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${ar(pc).contentName}}.`;return l.useEffect(()=>{var s;const r=(s=e.current)==null?void 0:s.getAttribute("aria-describedby");t&&r&&(document.getElementById(t)||console.warn(o))},[o,e,t]),null},gc=Ko,bl=Xo,hc=Zo,vc=Qo,bc=Jo,xc=tr,yc=or,wc=sr;function Sc(e){const t=l.useRef({value:e,previous:e});return l.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}var cr=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),Cc="VisuallyHidden",lr=l.forwardRef((e,t)=>g.jsx(G.span,{...e,ref:t,style:{...cr,...e.style}}));lr.displayName=Cc;var xl=lr;function yl({className:e,...t}){return g.jsx("div",{"data-slot":"card",className:Y("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function wl({className:e,...t}){return g.jsx("div",{"data-slot":"card-header",className:Y("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function Sl({className:e,...t}){return g.jsx("div",{"data-slot":"card-title",className:Y("leading-none font-semibold",e),...t})}function Cl({className:e,...t}){return g.jsx("div",{"data-slot":"card-description",className:Y("text-muted-foreground text-sm",e),...t})}function El({className:e,...t}){return g.jsx("div",{"data-slot":"card-content",className:Y("px-6",e),...t})}var Ec="Label",ur=l.forwardRef((e,t)=>g.jsx(G.label,{...e,ref:t,onMouseDown:n=>{var r;n.target.closest("button, input, select, textarea")||((r=e.onMouseDown)==null||r.call(e,n),!n.defaultPrevented&&n.detail>1&&n.preventDefault())}}));ur.displayName=Ec;var Rc=ur;function Rl({className:e,...t}){return g.jsx(Rc,{"data-slot":"label",className:Y("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}function Pl({className:e,type:t,...n}){return g.jsx("input",{type:t,"data-slot":"input",className:Y("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...n})}function Xn(e,[t,n]){return Math.min(n,Math.max(t,e))}var Pc=[" ","Enter","ArrowUp","ArrowDown"],Ac=[" ","Enter"],Me="Select",[Nt,Tt,kc]=zs(Me),[Ye,Al]=St(Me,[kc,ko]),Ot=ko(),[Nc,Pe]=Ye(Me),[Tc,Oc]=Ye(Me),dr=e=>{const{__scopeSelect:t,children:n,open:o,defaultOpen:r,onOpenChange:s,value:i,defaultValue:a,onValueChange:c,dir:u,name:f,autoComplete:p,disabled:m,required:h,form:x}=e,d=Ot(t),[v,y]=l.useState(null),[b,w]=l.useState(null),[C,R]=l.useState(!1),T=Bs(u),[P,E]=Gt({prop:o,defaultProp:r??!1,onChange:s,caller:Me}),[I,L]=Gt({prop:i,defaultProp:a,onChange:c,caller:Me}),V=l.useRef(null),B=v?x||!!v.closest("form"):!0,[z,_]=l.useState(new Set),$=Array.from(z).map(O=>O.props.value).join(";");return g.jsx(pa,{...d,children:g.jsxs(Nc,{required:h,scope:t,trigger:v,onTriggerChange:y,valueNode:b,onValueNodeChange:w,valueNodeHasChildren:C,onValueNodeHasChildrenChange:R,contentId:ze(),value:I,onValueChange:L,open:P,onOpenChange:E,dir:T,triggerPointerDownPosRef:V,disabled:m,children:[g.jsx(Nt.Provider,{scope:t,children:g.jsx(Tc,{scope:e.__scopeSelect,onNativeOptionAdd:l.useCallback(O=>{_(D=>new Set(D).add(O))},[]),onNativeOptionRemove:l.useCallback(O=>{_(D=>{const S=new Set(D);return S.delete(O),S})},[]),children:n})}),B?g.jsxs(Mr,{"aria-hidden":!0,required:h,tabIndex:-1,name:f,autoComplete:p,value:I,onChange:O=>L(O.target.value),disabled:m,form:x,children:[I===void 0?g.jsx("option",{value:""}):null,Array.from(z)]},$):null]})})};dr.displayName=Me;var fr="SelectTrigger",pr=l.forwardRef((e,t)=>{const{__scopeSelect:n,disabled:o=!1,...r}=e,s=Ot(n),i=Pe(fr,n),a=i.disabled||o,c=Q(t,i.onTriggerChange),u=Tt(n),f=l.useRef("touch"),[p,m,h]=_r(d=>{const v=u().filter(w=>!w.disabled),y=v.find(w=>w.value===i.value),b=Lr(v,d,y);b!==void 0&&i.onValueChange(b.value)}),x=d=>{a||(i.onOpenChange(!0),h()),d&&(i.triggerPointerDownPosRef.current={x:Math.round(d.pageX),y:Math.round(d.pageY)})};return g.jsx(ma,{asChild:!0,...s,children:g.jsx(G.button,{type:"button",role:"combobox","aria-controls":i.contentId,"aria-expanded":i.open,"aria-required":i.required,"aria-autocomplete":"none",dir:i.dir,"data-state":i.open?"open":"closed",disabled:a,"data-disabled":a?"":void 0,"data-placeholder":Dr(i.value)?"":void 0,...r,ref:c,onClick:K(r.onClick,d=>{d.currentTarget.focus(),f.current!=="mouse"&&x(d)}),onPointerDown:K(r.onPointerDown,d=>{f.current=d.pointerType;const v=d.target;v.hasPointerCapture(d.pointerId)&&v.releasePointerCapture(d.pointerId),d.button===0&&d.ctrlKey===!1&&d.pointerType==="mouse"&&(x(d),d.preventDefault())}),onKeyDown:K(r.onKeyDown,d=>{const v=p.current!=="";!(d.ctrlKey||d.altKey||d.metaKey)&&d.key.length===1&&m(d.key),!(v&&d.key===" ")&&Pc.includes(d.key)&&(x(),d.preventDefault())})})})});pr.displayName=fr;var mr="SelectValue",gr=l.forwardRef((e,t)=>{const{__scopeSelect:n,className:o,style:r,children:s,placeholder:i="",...a}=e,c=Pe(mr,n),{onValueNodeHasChildrenChange:u}=c,f=s!==void 0,p=Q(t,c.onValueNodeChange);return te(()=>{u(f)},[u,f]),g.jsx(G.span,{...a,ref:p,style:{pointerEvents:"none"},children:Dr(c.value)?g.jsx(g.Fragment,{children:i}):s})});gr.displayName=mr;var Ic="SelectIcon",hr=l.forwardRef((e,t)=>{const{__scopeSelect:n,children:o,...r}=e;return g.jsx(G.span,{"aria-hidden":!0,...r,ref:t,children:o||"▼"})});hr.displayName=Ic;var Mc="SelectPortal",vr=e=>g.jsx(hn,{asChild:!0,...e});vr.displayName=Mc;var De="SelectContent",br=l.forwardRef((e,t)=>{const n=Pe(De,e.__scopeSelect),[o,r]=l.useState();if(te(()=>{r(new DocumentFragment)},[]),!n.open){const s=o;return s?nt.createPortal(g.jsx(xr,{scope:e.__scopeSelect,children:g.jsx(Nt.Slot,{scope:e.__scopeSelect,children:g.jsx("div",{children:e.children})})}),s):null}return g.jsx(yr,{...e,ref:t})});br.displayName=De;var ae=10,[xr,Ae]=Ye(De),Dc="SelectContentImpl",_c=$e("SelectContent.RemoveScroll"),yr=l.forwardRef((e,t)=>{const{__scopeSelect:n,position:o="item-aligned",onCloseAutoFocus:r,onEscapeKeyDown:s,onPointerDownOutside:i,side:a,sideOffset:c,align:u,alignOffset:f,arrowPadding:p,collisionBoundary:m,collisionPadding:h,sticky:x,hideWhenDetached:d,avoidCollisions:v,...y}=e,b=Pe(De,n),[w,C]=l.useState(null),[R,T]=l.useState(null),P=Q(t,N=>C(N)),[E,I]=l.useState(null),[L,V]=l.useState(null),B=Tt(n),[z,_]=l.useState(!1),$=l.useRef(!1);l.useEffect(()=>{if(w)return Fo(w)},[w]),mo();const O=l.useCallback(N=>{const[U,...Z]=B().map(j=>j.ref.current),[W]=Z.slice(-1),F=document.activeElement;for(const j of N)if(j===F||(j==null||j.scrollIntoView({block:"nearest"}),j===U&&R&&(R.scrollTop=0),j===W&&R&&(R.scrollTop=R.scrollHeight),j==null||j.focus(),document.activeElement!==F))return},[B,R]),D=l.useCallback(()=>O([E,w]),[O,E,w]);l.useEffect(()=>{z&&D()},[z,D]);const{onOpenChange:S,triggerPointerDownPosRef:H}=b;l.useEffect(()=>{if(w){let N={x:0,y:0};const U=W=>{var F,j;N={x:Math.abs(Math.round(W.pageX)-(((F=H.current)==null?void 0:F.x)??0)),y:Math.abs(Math.round(W.pageY)-(((j=H.current)==null?void 0:j.y)??0))}},Z=W=>{N.x<=10&&N.y<=10?W.preventDefault():w.contains(W.target)||S(!1),document.removeEventListener("pointermove",U),H.current=null};return H.current!==null&&(document.addEventListener("pointermove",U),document.addEventListener("pointerup",Z,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",U),document.removeEventListener("pointerup",Z,{capture:!0})}}},[w,S,H]),l.useEffect(()=>{const N=()=>S(!1);return window.addEventListener("blur",N),window.addEventListener("resize",N),()=>{window.removeEventListener("blur",N),window.removeEventListener("resize",N)}},[S]);const[ee,se]=_r(N=>{const U=B().filter(F=>!F.disabled),Z=U.find(F=>F.ref.current===document.activeElement),W=Lr(U,N,Z);W&&setTimeout(()=>W.ref.current.focus())}),be=l.useCallback((N,U,Z)=>{const W=!$.current&&!Z;(b.value!==void 0&&b.value===U||W)&&(I(N),W&&($.current=!0))},[b.value]),q=l.useCallback(()=>w==null?void 0:w.focus(),[w]),X=l.useCallback((N,U,Z)=>{const W=!$.current&&!Z;(b.value!==void 0&&b.value===U||W)&&V(N)},[b.value]),ie=o==="popper"?Qt:wr,de=ie===Qt?{side:a,sideOffset:c,align:u,alignOffset:f,arrowPadding:p,collisionBoundary:m,collisionPadding:h,sticky:x,hideWhenDetached:d,avoidCollisions:v}:{};return g.jsx(xr,{scope:n,content:w,viewport:R,onViewportChange:T,itemRefCallback:be,selectedItem:E,onItemLeave:q,itemTextRefCallback:X,focusSelectedItem:D,selectedItemText:L,position:o,isPositioned:z,searchRef:ee,children:g.jsx(vn,{as:_c,allowPinchZoom:!0,children:g.jsx(sn,{asChild:!0,trapped:b.open,onMountAutoFocus:N=>{N.preventDefault()},onUnmountAutoFocus:K(r,N=>{var U;(U=b.trigger)==null||U.focus({preventScroll:!0}),N.preventDefault()}),children:g.jsx(rn,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:s,onPointerDownOutside:i,onFocusOutside:N=>N.preventDefault(),onDismiss:()=>b.onOpenChange(!1),children:g.jsx(ie,{role:"listbox",id:b.contentId,"data-state":b.open?"open":"closed",dir:b.dir,onContextMenu:N=>N.preventDefault(),...y,...de,onPlaced:()=>_(!0),ref:P,style:{display:"flex",flexDirection:"column",outline:"none",...y.style},onKeyDown:K(y.onKeyDown,N=>{const U=N.ctrlKey||N.altKey||N.metaKey;if(N.key==="Tab"&&N.preventDefault(),!U&&N.key.length===1&&se(N.key),["ArrowUp","ArrowDown","Home","End"].includes(N.key)){let W=B().filter(F=>!F.disabled).map(F=>F.ref.current);if(["ArrowUp","End"].includes(N.key)&&(W=W.slice().reverse()),["ArrowUp","ArrowDown"].includes(N.key)){const F=N.target,j=W.indexOf(F);W=W.slice(j+1)}setTimeout(()=>O(W)),N.preventDefault()}})})})})})})});yr.displayName=Dc;var Lc="SelectItemAlignedPosition",wr=l.forwardRef((e,t)=>{const{__scopeSelect:n,onPlaced:o,...r}=e,s=Pe(De,n),i=Ae(De,n),[a,c]=l.useState(null),[u,f]=l.useState(null),p=Q(t,P=>f(P)),m=Tt(n),h=l.useRef(!1),x=l.useRef(!0),{viewport:d,selectedItem:v,selectedItemText:y,focusSelectedItem:b}=i,w=l.useCallback(()=>{if(s.trigger&&s.valueNode&&a&&u&&d&&v&&y){const P=s.trigger.getBoundingClientRect(),E=u.getBoundingClientRect(),I=s.valueNode.getBoundingClientRect(),L=y.getBoundingClientRect();if(s.dir!=="rtl"){const F=L.left-E.left,j=I.left-F,ne=P.left-j,fe=P.width+ne,Xe=Math.max(fe,E.width),qe=window.innerWidth-ae,Ze=Xn(j,[ae,Math.max(ae,qe-Xe)]);a.style.minWidth=fe+"px",a.style.left=Ze+"px"}else{const F=E.right-L.right,j=window.innerWidth-I.right-F,ne=window.innerWidth-P.right-j,fe=P.width+ne,Xe=Math.max(fe,E.width),qe=window.innerWidth-ae,Ze=Xn(j,[ae,Math.max(ae,qe-Xe)]);a.style.minWidth=fe+"px",a.style.right=Ze+"px"}const V=m(),B=window.innerHeight-ae*2,z=d.scrollHeight,_=window.getComputedStyle(u),$=parseInt(_.borderTopWidth,10),O=parseInt(_.paddingTop,10),D=parseInt(_.borderBottomWidth,10),S=parseInt(_.paddingBottom,10),H=$+O+z+S+D,ee=Math.min(v.offsetHeight*5,H),se=window.getComputedStyle(d),be=parseInt(se.paddingTop,10),q=parseInt(se.paddingBottom,10),X=P.top+P.height/2-ae,ie=B-X,de=v.offsetHeight/2,N=v.offsetTop+de,U=$+O+N,Z=H-U;if(U<=X){const F=V.length>0&&v===V[V.length-1].ref.current;a.style.bottom="0px";const j=u.clientHeight-d.offsetTop-d.offsetHeight,ne=Math.max(ie,de+(F?q:0)+j+D),fe=U+ne;a.style.height=fe+"px"}else{const F=V.length>0&&v===V[0].ref.current;a.style.top="0px";const ne=Math.max(X,$+d.offsetTop+(F?be:0)+de)+Z;a.style.height=ne+"px",d.scrollTop=U-X+d.offsetTop}a.style.margin=`${ae}px 0`,a.style.minHeight=ee+"px",a.style.maxHeight=B+"px",o==null||o(),requestAnimationFrame(()=>h.current=!0)}},[m,s.trigger,s.valueNode,a,u,d,v,y,s.dir,o]);te(()=>w(),[w]);const[C,R]=l.useState();te(()=>{u&&R(window.getComputedStyle(u).zIndex)},[u]);const T=l.useCallback(P=>{P&&x.current===!0&&(w(),b==null||b(),x.current=!1)},[w,b]);return g.jsx(Fc,{scope:n,contentWrapper:a,shouldExpandOnScrollRef:h,onScrollButtonChange:T,children:g.jsx("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:C},children:g.jsx(G.div,{...r,ref:p,style:{boxSizing:"border-box",maxHeight:"100%",...r.style}})})})});wr.displayName=Lc;var jc="SelectPopperPosition",Qt=l.forwardRef((e,t)=>{const{__scopeSelect:n,align:o="start",collisionPadding:r=ae,...s}=e,i=Ot(n);return g.jsx(ga,{...i,...s,ref:t,align:o,collisionPadding:r,style:{boxSizing:"border-box",...s.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});Qt.displayName=jc;var[Fc,wn]=Ye(De,{}),Jt="SelectViewport",Sr=l.forwardRef((e,t)=>{const{__scopeSelect:n,nonce:o,...r}=e,s=Ae(Jt,n),i=wn(Jt,n),a=Q(t,s.onViewportChange),c=l.useRef(0);return g.jsxs(g.Fragment,{children:[g.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),g.jsx(Nt.Slot,{scope:n,children:g.jsx(G.div,{"data-radix-select-viewport":"",role:"presentation",...r,ref:a,style:{position:"relative",flex:1,overflow:"hidden auto",...r.style},onScroll:K(r.onScroll,u=>{const f=u.currentTarget,{contentWrapper:p,shouldExpandOnScrollRef:m}=i;if(m!=null&&m.current&&p){const h=Math.abs(c.current-f.scrollTop);if(h>0){const x=window.innerHeight-ae*2,d=parseFloat(p.style.minHeight),v=parseFloat(p.style.height),y=Math.max(d,v);if(y<x){const b=y+h,w=Math.min(x,b),C=b-w;p.style.height=w+"px",p.style.bottom==="0px"&&(f.scrollTop=C>0?C:0,p.style.justifyContent="flex-end")}}}c.current=f.scrollTop})})})]})});Sr.displayName=Jt;var Cr="SelectGroup",[zc,Wc]=Ye(Cr),Bc=l.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e,r=ze();return g.jsx(zc,{scope:n,id:r,children:g.jsx(G.div,{role:"group","aria-labelledby":r,...o,ref:t})})});Bc.displayName=Cr;var Er="SelectLabel",$c=l.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e,r=Wc(Er,n);return g.jsx(G.div,{id:r.id,...o,ref:t})});$c.displayName=Er;var wt="SelectItem",[Vc,Rr]=Ye(wt),Pr=l.forwardRef((e,t)=>{const{__scopeSelect:n,value:o,disabled:r=!1,textValue:s,...i}=e,a=Pe(wt,n),c=Ae(wt,n),u=a.value===o,[f,p]=l.useState(s??""),[m,h]=l.useState(!1),x=Q(t,b=>{var w;return(w=c.itemRefCallback)==null?void 0:w.call(c,b,o,r)}),d=ze(),v=l.useRef("touch"),y=()=>{r||(a.onValueChange(o),a.onOpenChange(!1))};if(o==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return g.jsx(Vc,{scope:n,value:o,disabled:r,textId:d,isSelected:u,onItemTextChange:l.useCallback(b=>{p(w=>w||((b==null?void 0:b.textContent)??"").trim())},[]),children:g.jsx(Nt.ItemSlot,{scope:n,value:o,disabled:r,textValue:f,children:g.jsx(G.div,{role:"option","aria-labelledby":d,"data-highlighted":m?"":void 0,"aria-selected":u&&m,"data-state":u?"checked":"unchecked","aria-disabled":r||void 0,"data-disabled":r?"":void 0,tabIndex:r?void 0:-1,...i,ref:x,onFocus:K(i.onFocus,()=>h(!0)),onBlur:K(i.onBlur,()=>h(!1)),onClick:K(i.onClick,()=>{v.current!=="mouse"&&y()}),onPointerUp:K(i.onPointerUp,()=>{v.current==="mouse"&&y()}),onPointerDown:K(i.onPointerDown,b=>{v.current=b.pointerType}),onPointerMove:K(i.onPointerMove,b=>{var w;v.current=b.pointerType,r?(w=c.onItemLeave)==null||w.call(c):v.current==="mouse"&&b.currentTarget.focus({preventScroll:!0})}),onPointerLeave:K(i.onPointerLeave,b=>{var w;b.currentTarget===document.activeElement&&((w=c.onItemLeave)==null||w.call(c))}),onKeyDown:K(i.onKeyDown,b=>{var C;((C=c.searchRef)==null?void 0:C.current)!==""&&b.key===" "||(Ac.includes(b.key)&&y(),b.key===" "&&b.preventDefault())})})})})});Pr.displayName=wt;var Je="SelectItemText",Ar=l.forwardRef((e,t)=>{const{__scopeSelect:n,className:o,style:r,...s}=e,i=Pe(Je,n),a=Ae(Je,n),c=Rr(Je,n),u=Oc(Je,n),[f,p]=l.useState(null),m=Q(t,y=>p(y),c.onItemTextChange,y=>{var b;return(b=a.itemTextRefCallback)==null?void 0:b.call(a,y,c.value,c.disabled)}),h=f==null?void 0:f.textContent,x=l.useMemo(()=>g.jsx("option",{value:c.value,disabled:c.disabled,children:h},c.value),[c.disabled,c.value,h]),{onNativeOptionAdd:d,onNativeOptionRemove:v}=u;return te(()=>(d(x),()=>v(x)),[d,v,x]),g.jsxs(g.Fragment,{children:[g.jsx(G.span,{id:c.textId,...s,ref:m}),c.isSelected&&i.valueNode&&!i.valueNodeHasChildren?nt.createPortal(s.children,i.valueNode):null]})});Ar.displayName=Je;var kr="SelectItemIndicator",Nr=l.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e;return Rr(kr,n).isSelected?g.jsx(G.span,{"aria-hidden":!0,...o,ref:t}):null});Nr.displayName=kr;var en="SelectScrollUpButton",Tr=l.forwardRef((e,t)=>{const n=Ae(en,e.__scopeSelect),o=wn(en,e.__scopeSelect),[r,s]=l.useState(!1),i=Q(t,o.onScrollButtonChange);return te(()=>{if(n.viewport&&n.isPositioned){let a=function(){const u=c.scrollTop>0;s(u)};const c=n.viewport;return a(),c.addEventListener("scroll",a),()=>c.removeEventListener("scroll",a)}},[n.viewport,n.isPositioned]),r?g.jsx(Ir,{...e,ref:i,onAutoScroll:()=>{const{viewport:a,selectedItem:c}=n;a&&c&&(a.scrollTop=a.scrollTop-c.offsetHeight)}}):null});Tr.displayName=en;var tn="SelectScrollDownButton",Or=l.forwardRef((e,t)=>{const n=Ae(tn,e.__scopeSelect),o=wn(tn,e.__scopeSelect),[r,s]=l.useState(!1),i=Q(t,o.onScrollButtonChange);return te(()=>{if(n.viewport&&n.isPositioned){let a=function(){const u=c.scrollHeight-c.clientHeight,f=Math.ceil(c.scrollTop)<u;s(f)};const c=n.viewport;return a(),c.addEventListener("scroll",a),()=>c.removeEventListener("scroll",a)}},[n.viewport,n.isPositioned]),r?g.jsx(Ir,{...e,ref:i,onAutoScroll:()=>{const{viewport:a,selectedItem:c}=n;a&&c&&(a.scrollTop=a.scrollTop+c.offsetHeight)}}):null});Or.displayName=tn;var Ir=l.forwardRef((e,t)=>{const{__scopeSelect:n,onAutoScroll:o,...r}=e,s=Ae("SelectScrollButton",n),i=l.useRef(null),a=Tt(n),c=l.useCallback(()=>{i.current!==null&&(window.clearInterval(i.current),i.current=null)},[]);return l.useEffect(()=>()=>c(),[c]),te(()=>{var f;const u=a().find(p=>p.ref.current===document.activeElement);(f=u==null?void 0:u.ref.current)==null||f.scrollIntoView({block:"nearest"})},[a]),g.jsx(G.div,{"aria-hidden":!0,...r,ref:t,style:{flexShrink:0,...r.style},onPointerDown:K(r.onPointerDown,()=>{i.current===null&&(i.current=window.setInterval(o,50))}),onPointerMove:K(r.onPointerMove,()=>{var u;(u=s.onItemLeave)==null||u.call(s),i.current===null&&(i.current=window.setInterval(o,50))}),onPointerLeave:K(r.onPointerLeave,()=>{c()})})}),Hc="SelectSeparator",Uc=l.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e;return g.jsx(G.div,{"aria-hidden":!0,...o,ref:t})});Uc.displayName=Hc;var nn="SelectArrow",Gc=l.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e,r=Ot(n),s=Pe(nn,n),i=Ae(nn,n);return s.open&&i.position==="popper"?g.jsx(ha,{...r,...o,ref:t}):null});Gc.displayName=nn;var Kc="SelectBubbleInput",Mr=l.forwardRef(({__scopeSelect:e,value:t,...n},o)=>{const r=l.useRef(null),s=Q(o,r),i=Sc(t);return l.useEffect(()=>{const a=r.current;if(!a)return;const c=window.HTMLSelectElement.prototype,f=Object.getOwnPropertyDescriptor(c,"value").set;if(i!==t&&f){const p=new Event("change",{bubbles:!0});f.call(a,t),a.dispatchEvent(p)}},[i,t]),g.jsx(G.select,{...n,style:{...cr,...n.style},ref:s,defaultValue:t})});Mr.displayName=Kc;function Dr(e){return e===""||e===void 0}function _r(e){const t=Te(e),n=l.useRef(""),o=l.useRef(0),r=l.useCallback(i=>{const a=n.current+i;t(a),function c(u){n.current=u,window.clearTimeout(o.current),u!==""&&(o.current=window.setTimeout(()=>c(""),1e3))}(a)},[t]),s=l.useCallback(()=>{n.current="",window.clearTimeout(o.current)},[]);return l.useEffect(()=>()=>window.clearTimeout(o.current),[]),[n,r,s]}function Lr(e,t,n){const r=t.length>1&&Array.from(t).every(u=>u===t[0])?t[0]:t,s=n?e.indexOf(n):-1;let i=Yc(e,Math.max(s,0));r.length===1&&(i=i.filter(u=>u!==n));const c=i.find(u=>u.textValue.toLowerCase().startsWith(r.toLowerCase()));return c!==n?c:void 0}function Yc(e,t){return e.map((n,o)=>e[(t+o)%e.length])}var Xc=dr,qc=pr,Zc=gr,Qc=hr,Jc=vr,el=br,tl=Sr,nl=Pr,ol=Ar,rl=Nr,sl=Tr,il=Or;function kl({...e}){return g.jsx(Xc,{"data-slot":"select",...e})}function Nl({...e}){return g.jsx(Zc,{"data-slot":"select-value",...e})}function Tl({className:e,size:t="default",children:n,...o}){return g.jsxs(qc,{"data-slot":"select-trigger","data-size":t,className:Y("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...o,children:[n,g.jsx(Qc,{asChild:!0,children:g.jsx(Zn,{className:"size-4 opacity-50"})})]})}function Ol({className:e,children:t,position:n="popper",...o}){return g.jsx(Jc,{children:g.jsxs(el,{"data-slot":"select-content",className:Y("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md",n==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:n,...o,children:[g.jsx(al,{}),g.jsx(tl,{className:Y("p-1",n==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),g.jsx(cl,{})]})})}function Il({className:e,children:t,...n}){return g.jsxs(nl,{"data-slot":"select-item",className:Y("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...n,children:[g.jsx("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:g.jsx(rl,{children:g.jsx(Fr,{className:"size-4"})})}),g.jsx(ol,{children:t})]})}function al({className:e,...t}){return g.jsx(sl,{"data-slot":"select-scroll-up-button",className:Y("flex cursor-default items-center justify-center py-1",e),...t,children:g.jsx(zr,{className:"size-4"})})}function cl({className:e,...t}){return g.jsx(il,{"data-slot":"select-scroll-down-button",className:Y("flex cursor-default items-center justify-center py-1",e),...t,children:g.jsx(Zn,{className:"size-4"})})}function Ml({...e}){return g.jsx(gc,{"data-slot":"dialog",...e})}function ll({...e}){return g.jsx(hc,{"data-slot":"dialog-portal",...e})}function ul({className:e,...t}){return g.jsx(vc,{"data-slot":"dialog-overlay",className:Y("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function Dl({className:e,children:t,showCloseButton:n=!0,...o}){return g.jsxs(ll,{"data-slot":"dialog-portal",children:[g.jsx(ul,{}),g.jsxs(bc,{"data-slot":"dialog-content",className:Y("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...o,children:[t,n&&g.jsxs(wc,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[g.jsx(Wr,{}),g.jsx("span",{className:"sr-only",children:"Close"})]})]})]})}function _l({className:e,...t}){return g.jsx("div",{"data-slot":"dialog-header",className:Y("flex flex-col gap-2 text-center sm:text-left",e),...t})}function Ll({className:e,...t}){return g.jsx(xc,{"data-slot":"dialog-title",className:Y("text-lg leading-none font-semibold",e),...t})}function jl({className:e,...t}){return g.jsx(yc,{"data-slot":"dialog-description",className:Y("text-muted-foreground text-sm",e),...t})}function Fl({className:e,...t}){return g.jsx("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:g.jsx("table",{"data-slot":"table",className:Y("w-full caption-bottom text-sm",e),...t})})}function zl({className:e,...t}){return g.jsx("thead",{"data-slot":"table-header",className:Y("[&_tr]:border-b",e),...t})}function Wl({className:e,...t}){return g.jsx("tbody",{"data-slot":"table-body",className:Y("[&_tr:last-child]:border-0",e),...t})}function Bl({className:e,...t}){return g.jsx("tr",{"data-slot":"table-row",className:Y("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function $l({className:e,...t}){return g.jsx("th",{"data-slot":"table-head",className:Y("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function Vl({className:e,...t}){return g.jsx("td",{"data-slot":"table-cell",className:Y("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}export{Ll as $,ma as A,gl as B,ga as C,rn as D,Ls as E,sn as F,Xr as G,yl as H,wl as I,Sl as J,Cl as K,El as L,Rl as M,Pl as N,vc as O,G as P,Tl as Q,pa as R,kl as S,bl as T,Nl as U,Ol as V,Il as W,Ts as X,Ml as Y,Dl as Z,_l as _,zs as a,jl as a0,ia as a1,Fl as a2,zl as a3,Bl as a4,$l as a5,Wl as a6,Vl as a7,Vr as a8,K as b,St as c,Q as d,Bs as e,Gt as f,Te as g,ko as h,Pt as i,hn as j,Fo as k,Fs as l,mo as m,vn as n,$e as o,ha as p,Qn as q,Y as r,gc as s,bc as t,ze as u,wc as v,hc as w,xl as x,Sc as y,te as z};
