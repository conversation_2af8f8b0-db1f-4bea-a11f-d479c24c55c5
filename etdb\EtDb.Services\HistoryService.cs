﻿namespace EtDb.Services
{
    using EtDb.DataHandlers.Interfaces;
    using EtDb.DataHandlers.Models;
    using EtDb.Services.Interfaces;

    public class HistoryService : IHistoryService
    {
        private readonly Lazy<IHistoryDataHandler> historyDataHandler;
        private readonly Lazy<IMapper> mapper;

        public HistoryService(Lazy<IHistoryDataHandler> historyDataHand<PERSON>, Lazy<IMapper> mapper)
        {
            this.historyDataHandler = historyDataHandler;
            this.mapper = mapper;
        }

        public async Task UpdateHistoryTransfersToBlockedUsersAsync(IEnumerable<string> userIds)
        {
            await this.historyDataHandler.Value.UpdateHistoryTransfersToBlockedUsersAsync(userIds);
        }

        public async Task<IDictionary<string, TransferedItemsModel>> CancelHistoryExpiredTransfersAsync(double cancellationDays)
        {
            var result = await this.historyDataHandler.Value.CancelHistoryExpiredTransfersAsync(cancellationDays);

            return result.Value;
        }

        public async Task<IEnumerable<HistoryDto>> GetReservedItemsByFromUserIdAsync(string userId)
        {
            var res = this.historyDataHandler.Value
                .GetHistoryResrvedItemsByFromUserId(userId);
            return await Task.FromResult(this.mapper.Value.Map<IEnumerable<HistoryDto>>(res));
        }
    }
}
