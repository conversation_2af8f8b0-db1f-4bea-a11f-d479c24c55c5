import{j as a}from"./query-vendor-DbpRcGHB.js";import{u as N}from"./form-vendor-mwNakpFF.js";import{c as j}from"./index-CvPR-Eda.js";import{r as l}from"./router-vendor-CX6yTN5-.js";import{M as O}from"./ui-components-B3WDh88j.js";import{F as C}from"./filtered-dropdown-BBzS_ulI.js";import{u as F}from"./i18n-vendor-CzM1WOJE.js";import{j as v}from"./ui-vendor-BwzdWZX0.js";import"./react-vendor-DJG_os-6.js";import"./api-client-xsH4HHeE.js";import"./etws-api-BUsVSvPp.js";import"./badge-DNy6GwuQ.js";const B=()=>{var c,m;const{t:r}=F(),[t,p]=l.useState(""),{register:u,setValue:d,watch:h,formState:{errors:s}}=N(),{data:n,isLoading:f}=j(t),i=!!s.nameOfTechnician,g=(m=(c=s.nameOfTechnician)==null?void 0:c.message)==null?void 0:m.toString(),o=h("nameOfTechnician")||"",x=l.useMemo(()=>n?n.map(e=>({value:e.displayName||"",label:`${e.displayName} - ${e.iptuName} - ${e.eln}`,group:e.iptuName||"Other",originalData:e})):[],[n]),T=e=>{const y=Array.isArray(e)?e[0]:e;d("nameOfTechnician",y,{shouldValidate:!0})};return a.jsxs("div",{className:"space-y-2",children:[a.jsx(O,{htmlFor:"nameOfTechnician",children:r("enterFirstAndLastName")}),a.jsx(C,{filterType:"external",options:x,value:o,onValueChange:T,placeholder:r("searchTechnicians"),searchPlaceholder:r("searchTechnicians"),emptyText:r("noTechniciansFound"),loadingText:r("loading"),groupBy:"group",clearable:!0,loading:f,searchQuery:t,onSearchQueryChange:p,triggerClassName:i?"border-destructive":""}),a.jsx("input",{type:"hidden",...u("nameOfTechnician"),value:o}),i&&a.jsxs("div",{className:"flex items-center text-sm text-destructive",children:[a.jsx(v,{className:"h-4 w-4 mr-1.5"}),r(g||"")]})]})};export{B as default};
