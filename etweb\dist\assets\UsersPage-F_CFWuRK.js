const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/UserEditDialog-BXdcfQzn.js","assets/query-vendor-DbpRcGHB.js","assets/router-vendor-CX6yTN5-.js","assets/react-vendor-DJG_os-6.js","assets/etws-api-BUsVSvPp.js","assets/api-client-xsH4HHeE.js","assets/ui-components-B3WDh88j.js","assets/ui-vendor-BwzdWZX0.js","assets/i18n-vendor-CzM1WOJE.js","assets/form-vendor-mwNakpFF.js","assets/index-CvPR-Eda.js","assets/index-DQLga5a_.css","assets/UserTable-CfQ7ec3x.js","assets/badge-DNy6GwuQ.js"])))=>i.map(i=>d[i]);
import{_ as Y}from"./index-CvPR-Eda.js";import{j as e,a as V,c as D,u as L}from"./query-vendor-DbpRcGHB.js";import{r as x}from"./router-vendor-CX6yTN5-.js";import{B as P,S as q,Q as z,U as T,V as A,W as S,N as re,r as F,X as le,Y as ie,Z as ce}from"./ui-components-B3WDh88j.js";import{u as R}from"./i18n-vendor-CzM1WOJE.js";import{F as oe,X,h as de,E as ue,i as xe,j as me,R as he,k as pe,l as fe}from"./ui-vendor-BwzdWZX0.js";import{U as je,S as ge}from"./etws-api-BUsVSvPp.js";function ve({columns:s,filters:t,onFiltersChange:i,onQueryChange:c,sortBy:g,sortDir:j,onSortChange:v,sortOptions:N}){const{t:o}=R(),[m,p]=x.useState(!1),E=a=>a.length===0?"":a.filter(n=>n.value.trim()!=="").map(n=>`${n.field}+${n.operator}+${n.value}`).join(","),w=(a,n,r)=>{const l=t.findIndex(U=>U.field===a);let f;if(r.trim()==="")if(l>=0)f=t.filter(U=>U.field!==a);else return;else{const U={field:a,operator:n,value:r};l>=0?(f=[...t],f[l]=U):f=[...t,U]}i(f),c&&c(E(f))},_=a=>{const n=t.filter(r=>r.field!==a);i(n),c&&c(E(n))},d=()=>{i([]),c&&c("")},b=a=>{var n;return((n=t.find(r=>r.field===a))==null?void 0:n.value)||""},u=a=>{var n;return((n=t.find(r=>r.field===a))==null?void 0:n.operator)||"1"},k=(a,n,r)=>e.jsxs("div",{className:"flex gap-1 items-center flex-grow",children:[e.jsxs(q,{value:n,onValueChange:l=>w(a.field,"0",l),children:[e.jsx(z,{id:r,className:"h-8 text-xs flex-grow",children:e.jsx(T,{placeholder:`${o("select")}...`})}),e.jsx(A,{children:a.options.map(l=>e.jsx(S,{value:l.value,className:"text-xs",children:l.label},l.value))})]}),n&&e.jsx(P,{variant:"ghost",size:"sm",onClick:()=>_(a.field),className:"h-8 w-8 p-0 flex-shrink-0",children:e.jsx(X,{className:"h-3 w-3"})})]}),I=(a,n,r,l)=>e.jsxs("div",{className:"relative group flex-grow",children:[e.jsxs("div",{className:"flex items-center border border-input rounded-md bg-background hover:border-accent-foreground/30 transition-all duration-200",children:[e.jsxs(q,{value:r,onValueChange:f=>w(a.field,f,n),children:[e.jsx(z,{className:"border-0 h-7 w-10 text-xs bg-transparent rounded-r-none border-r border-border/50 focus:ring-0 hover:bg-accent/50 flex-shrink-0",children:e.jsx(T,{})}),e.jsxs(A,{children:[e.jsx(S,{value:"0",className:"text-xs",children:"≡"}),e.jsx(S,{value:"1",className:"text-xs",children:"∋"}),e.jsx(S,{value:"2",className:"text-xs",children:"$"}),e.jsx(S,{value:"3",className:"text-xs",children:"^"})]})]}),e.jsx(re,{id:l,placeholder:"...",value:n,onChange:f=>w(a.field,r,f.target.value),className:"border-0 h-7 text-xs bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 placeholder:text-muted-foreground/50 flex-grow rounded-none"}),n&&e.jsx(P,{variant:"ghost",size:"sm",onClick:()=>_(a.field),className:"h-5 w-5 p-0 mr-1 opacity-0 group-hover:opacity-100 hover:bg-destructive/10 hover:text-destructive transition-all duration-200 rounded-sm flex-shrink-0",children:e.jsx(X,{className:"h-3 w-3"})})]}),n&&e.jsxs("div",{className:"absolute -top-6 left-0 bg-popover text-popover-foreground text-xs px-2 py-0.5 rounded border shadow-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-20 whitespace-nowrap",children:[r==="0"&&"= "+o("equals"),r==="1"&&"∋ "+o("contains"),r==="2"&&"$ "+o("endsWith"),r==="3"&&"^ "+o("startsWith")]})]}),y=(a,n)=>{const r=b(a.field),l=u(a.field);return a.type==="select"&&a.options?k(a,r,n):I(a,r,l,n)};return e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between gap-2",children:[e.jsxs(P,{variant:"outline",size:"sm",onClick:()=>p(!m),className:"h-8",children:[e.jsx(oe,{className:"h-3 w-3 mr-1"}),o("filters")," ",t.length>0&&`(${t.length})`]}),e.jsxs("div",{className:"flex items-center gap-2 text-xs",children:[e.jsxs("span",{className:"text-muted-foreground",children:[o("sortBy"),":"]}),e.jsxs(q,{value:g,onValueChange:a=>v(a,j),children:[e.jsx(z,{className:"h-8 w-32 text-xs",children:e.jsx(T,{})}),e.jsx(A,{children:N.map(a=>e.jsx(S,{value:a.value,className:"text-xs",children:a.label},a.value))})]}),e.jsxs(q,{value:j,onValueChange:a=>v(g,a),children:[e.jsx(z,{className:"h-8 w-20 text-xs",children:e.jsx(T,{})}),e.jsxs(A,{children:[e.jsx(S,{value:"asc",className:"text-xs",children:o("ascending")}),e.jsx(S,{value:"desc",className:"text-xs",children:o("descending")})]})]})]})]}),m&&e.jsxs("div",{className:"border rounded-md p-3 space-y-3 bg-muted/20",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-xs font-medium text-muted-foreground uppercase tracking-wide",children:o("columnFilters")}),e.jsx(P,{variant:"ghost",size:"sm",onClick:d,disabled:t.length===0,className:"h-7 text-xs",children:o("clearAll")})]}),e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-x-4 gap-y-3",children:s.map(a=>{const n=`filter-input-${a.field}`;return e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("label",{htmlFor:n,className:"text-xs font-medium text-muted-foreground whitespace-nowrap flex-shrink-0",children:[a.label,":"]}),e.jsx("div",{className:"flex-grow min-w-0",children:y(a,n)})]},a.field)})})]})]})}function Ne({className:s,...t}){return e.jsx("nav",{role:"navigation","aria-label":"pagination","data-slot":"pagination",className:F("mx-auto flex w-full justify-center",s),...t})}function be({className:s,...t}){return e.jsx("ul",{"data-slot":"pagination-content",className:F("flex flex-row items-center gap-1",s),...t})}function M({...s}){return e.jsx("li",{"data-slot":"pagination-item",...s})}function K({className:s,isActive:t,size:i="icon",...c}){return e.jsx("a",{"aria-current":t?"page":void 0,"data-slot":"pagination-link","data-active":t,className:F(le({variant:t?"outline":"ghost",size:i}),s),...c})}function ye({className:s,...t}){return e.jsxs(K,{"aria-label":"Go to previous page",size:"default",className:F("gap-1 px-2.5 sm:pl-2.5",s),...t,children:[e.jsx(de,{}),e.jsx("span",{className:"hidden sm:block",children:"Previous"})]})}function Se({className:s,...t}){return e.jsxs(K,{"aria-label":"Go to next page",size:"default",className:F("gap-1 px-2.5 sm:pr-2.5",s),...t,children:[e.jsx("span",{className:"hidden sm:block",children:"Next"}),e.jsx(xe,{})]})}function we({className:s,...t}){return e.jsxs("span",{"aria-hidden":!0,"data-slot":"pagination-ellipsis",className:F("flex size-9 items-center justify-center",s),...t,children:[e.jsx(ue,{className:"size-4"}),e.jsx("span",{className:"sr-only",children:"More pages"})]})}function ke({currentPage:s,totalPages:t,pageSize:i,totalCount:c,onPageChange:g,onPageSizeChange:j,showPageSizeSelector:v=!1,pageSizeOptions:N=[10,20,50,100],showResultsInfo:o=!0,className:m=""}){const{t:p}=R(),E=(s-1)*i+1,w=Math.min(s*i,c),_=()=>{const b=[],u=[],k=Math.max(1,s-2),I=Math.min(t,s+2);for(let y=k;y<=I;y++)b.push(y);return k>1&&(u.push(1),k>2&&u.push("...")),u.push(...b),I<t&&(I<t-1&&u.push("..."),u.push(t)),u};return t<=1&&!v?null:e.jsxs("div",{className:`flex flex-col sm:flex-row items-center justify-between gap-4 ${m}`,children:[o&&e.jsx("div",{className:"text-sm text-muted-foreground",children:c>0?p("showingResults",{start:E,end:w,total:c}):p("noResults")}),v&&j&&e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("span",{className:"text-sm text-muted-foreground",children:[p("itemsPerPage"),":"]}),e.jsxs(q,{value:i.toString(),onValueChange:d=>j(parseInt(d)),children:[e.jsx(z,{className:"w-20",children:e.jsx(T,{})}),e.jsx(A,{children:N.map(d=>e.jsx(S,{value:d.toString(),children:d},d))})]})]}),t>1&&e.jsx(Ne,{className:"justify-end",children:e.jsxs(be,{children:[e.jsx(M,{children:e.jsx(ye,{onClick:()=>g(Math.max(1,s-1)),className:s===1?"pointer-events-none opacity-50":"cursor-pointer"})}),_().map((d,b)=>{if(d==="...")return e.jsx(M,{children:e.jsx(we,{})},`dots-${b}`);const u=d;return e.jsx(M,{children:e.jsx(K,{onClick:()=>g(u),isActive:s===u,className:"cursor-pointer",children:u})},u)}),e.jsx(M,{children:e.jsx(Se,{onClick:()=>g(Math.min(t,s+1)),className:s===t?"pointer-events-none opacity-50":"cursor-pointer"})})]})})]})}const B=new je(void 0,"/et-ws",void 0),Ue=s=>V({queryKey:["users","search",s],queryFn:async({queryKey:t})=>{const i=t[2];return(await B.apiUsersSearchPost(i)).data},enabled:!0}),De=s=>V({queryKey:["users",s],queryFn:async()=>(await B.apiUsersByUserIdGet(s)).data,enabled:!!s}),Le=()=>{const s=D();return L({mutationFn:async t=>(await B.apiUsersPut(t)).data,onSuccess:()=>{s.invalidateQueries({queryKey:["users"]})},onError:t=>{console.error("Error updating user:",t)}})},Ce=()=>{const s=D();return L({mutationFn:async t=>(await B.apiUsersActivateUsersPut(t)).data,onSuccess:()=>{s.invalidateQueries({queryKey:["users"]})},onError:t=>{console.error("Error activating users:",t)}})},Fe=()=>{const s=D();return L({mutationFn:async t=>(await B.apiUsersBlockUsersPut(t)).data,onSuccess:()=>{s.invalidateQueries({queryKey:["users"]})},onError:t=>{console.error("Error blocking users:",t)}})},Ee=new ge(void 0,"/et-ws",void 0),Ie=()=>V({queryKey:["schenkers","select-list"],queryFn:async()=>(await Ee.apiSchenkersSelectListGet()).data});function Pe({message:s,title:t,onRetry:i,size:c="md",className:g}){const{t:j}=R(),v=s||j("common.error.loadingData"),N={sm:"p-4",md:"p-8",lg:"p-12"},o={sm:"h-4 w-4",md:"h-5 w-5",lg:"h-6 w-6"};return e.jsx("div",{className:F("flex flex-col items-center justify-center text-center",N[c],g),children:e.jsxs("div",{className:"flex flex-col items-center gap-4",children:[e.jsxs("div",{className:"flex items-center gap-3 text-destructive",children:[e.jsx(me,{className:F(o[c])}),e.jsxs("div",{children:[t&&e.jsx("h3",{className:"font-medium text-foreground mb-1",children:t}),e.jsx("span",{className:"text-sm",children:v})]})]}),i&&e.jsxs(P,{variant:"outline",size:"sm",onClick:i,className:"flex items-center gap-2",children:[e.jsx(he,{className:"h-4 w-4"}),j("common.error.retryButton")]})]})})}const _e=x.lazy(()=>Y(()=>import("./UserEditDialog-BXdcfQzn.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11])).then(s=>({default:s.UserEditDialog}))),qe=x.lazy(()=>Y(()=>import("./UserTable-CfQ7ec3x.js"),__vite__mapDeps([12,1,2,3,6,7,10,8,5,4,11,13])));function ze(){const{t:s}=R(),[t,i]=x.useState([]),[c,g]=x.useState(""),[j,v]=x.useState(1),[N,o]=x.useState(10),[m,p]=x.useState([]),[E,w]=x.useState(null),[_,d]=x.useState(!1),[b,u]=x.useState("Eln"),[k,I]=x.useState("asc"),{data:y}=Ie(),a=[{field:"Eln",label:s("eln")},{field:"FirstName",label:s("firstName")},{field:"Surname",label:s("surname")},{field:"FamilyName",label:s("familyName")},{field:"SchenkerId",label:s("op"),type:"select",options:(y==null?void 0:y.map(h=>{var C;return{value:((C=h.id)==null?void 0:C.toString())||"",label:h.opCode??""}}))||[]},{field:"City.Region",label:s("region"),type:"select",options:[{value:"1",label:"Север"},{value:"2",label:"Юг"},{value:"3",label:"Изток"},{value:"4",label:"Запад"},{value:"5",label:"София"}]},{field:"IPTUName",label:s("iptuName")},{field:"City.Name",label:s("city")},{field:"Blocked",label:s("status"),type:"select",options:[{value:"0",label:s("active")},{value:"1",label:s("blocked")}]}],n=[{value:"Eln",label:s("eln")},{value:"FirstName",label:s("firstName")},{value:"Surname",label:s("surname")},{value:"FamilyName",label:s("familyName")},{value:"SchenkerId",label:s("op")},{value:"City.Region",label:s("region")},{value:"IPTUName",label:s("iptuName")},{value:"City.Name",label:s("city")},{value:"ClientNumber",label:s("clientNumber")},{value:"Blocked",label:s("status")}],r={pageNumber:j,pageSize:N,query:c,sortBy:b,sortDir:k},{data:l,isLoading:f,error:U,refetch:Z}=Ue(r),O=Ce(),G=Fe(),Q=(l==null?void 0:l.dataCollection)||[],W=(l==null?void 0:l.count)||0,H=Math.ceil(W/N),J=(h,C)=>{p(C?$=>[...$,h]:$=>$.filter(ne=>ne!==h))},ee=h=>{p(h?Q.map(C=>C.id||""):[])},se=h=>{w(h.id||""),d(!0)},te=()=>{m.length>0&&O.mutate(m,{onSuccess:()=>{p([])}})},ae=()=>{m.length>0&&G.mutate(m,{onSuccess:()=>{p([])}})};return U?e.jsx(Pe,{title:s("errorLoadingUsers"),onRetry:()=>Z()}):e.jsxs("div",{className:"container mx-auto py-8 space-y-6",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold",children:s("users")}),e.jsx("p",{className:"text-muted-foreground",children:s("manageSystemUsers")})]})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"flex items-center justify-end gap-2",children:m.length>0&&e.jsxs(e.Fragment,{children:[e.jsxs(P,{variant:"outline",size:"sm",onClick:te,disabled:O.isPending,children:[e.jsx(pe,{className:"h-4 w-4 mr-2"}),s("activate")," (",m.length,")"]}),e.jsxs(P,{variant:"outline",size:"sm",onClick:ae,disabled:G.isPending,children:[e.jsx(fe,{className:"h-4 w-4 mr-2"}),s("block")," (",m.length,")"]})]})}),e.jsx(ve,{columns:a,filters:t,onFiltersChange:i,onQueryChange:g,sortBy:b,sortDir:k,onSortChange:(h,C)=>{u(h),I(C)},sortOptions:n})]}),e.jsx(x.Suspense,{fallback:e.jsx("div",{className:"flex items-center justify-center p-8",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"})}),children:e.jsx(qe,{users:Q,isLoading:f,selectedUsers:m,handleSelectUser:J,handleSelectAll:ee,handleEditUser:se})}),e.jsx(ke,{currentPage:j,totalPages:H,pageSize:N,totalCount:W,onPageChange:v,onPageSizeChange:h=>{o(h),v(1)},showPageSizeSelector:!0,showResultsInfo:!0,pageSizeOptions:[10,20,50,100]}),e.jsx(ie,{open:_,onOpenChange:d,children:e.jsx(ce,{className:"max-w-2xl",children:E&&e.jsx(x.Suspense,{fallback:e.jsx("div",{className:"flex items-center justify-center p-8",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"})}),children:e.jsx(_e,{userId:E,onClose:()=>{d(!1),w(null)}})})})})]})}const Ke=Object.freeze(Object.defineProperty({__proto__:null,default:ze},Symbol.toStringTag,{value:"Module"}));export{Pe as E,Ke as U,Ie as a,De as b,Le as u};
