import{j as e}from"./query-vendor-DbpRcGHB.js";import{u as j}from"./form-vendor-mwNakpFF.js";import{e as v}from"./index-CvPR-Eda.js";import{r as p}from"./router-vendor-CX6yTN5-.js";import{M as N}from"./ui-components-B3WDh88j.js";import{F as b}from"./filtered-dropdown-BBzS_ulI.js";import{u as y}from"./i18n-vendor-CzM1WOJE.js";import{f as u}from"./ui-vendor-BwzdWZX0.js";import"./react-vendor-DJG_os-6.js";import"./api-client-xsH4HHeE.js";import"./etws-api-BUsVSvPp.js";import"./badge-DNy6GwuQ.js";const $=()=>{var n,l;const{data:s,isLoading:d,error:a}=v(),{t}=y(),{watch:x,setValue:i,formState:{errors:m}}=j(),h=!!m.opId,f=(l=(n=m.opId)==null?void 0:n.message)==null?void 0:l.toString(),c=x("userId"),o=s==null?void 0:s.find(r=>r.id===c),g=p.useMemo(()=>s?s.map(r=>({value:r.id||"",label:`${r.displayName} - ${r.opcode}`,group:r.iptuName||"Other",originalData:r})):[],[s]);return p.useEffect(()=>{o!=null&&o.opcode&&i("deliveryShop",o.opcode)},[o,i]),e.jsxs("div",{className:"space-y-2",children:[e.jsx(N,{htmlFor:"opId",children:t("selectATechnicianFromTheList")}),e.jsxs("div",{children:[d?e.jsxs("div",{className:"flex items-center space-x-2 py-3",children:[e.jsx("div",{className:"h-4 w-4 animate-spin rounded-full border-2 border-indigo-500 border-t-transparent"}),e.jsx("span",{className:"text-sm text-gray-500",children:t("loading")})]}):a?e.jsxs("div",{className:"flex items-center rounded-lg border border-red-200 bg-red-50 px-4 py-3 text-sm text-destructive",children:[e.jsx(u,{className:"mr-2 h-5 w-5"}),e.jsx("span",{children:a.message})]}):e.jsx(b,{options:g,value:c||"",onValueChange:r=>i("userId",r),placeholder:t("selectOperation"),groupBy:"group",clearable:!0,showSearch:!0,searchPlaceholder:t("search"),emptyText:t("noOptionsFound")}),h&&!d&&!a&&e.jsxs("div",{className:"mt-1.5 flex items-center text-sm text-destructive",children:[e.jsx(u,{className:"mr-1.5 h-4 w-4"}),e.jsx("p",{children:t(f||"")})]})]})]})};export{$ as default};
