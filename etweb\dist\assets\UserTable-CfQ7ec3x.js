import{j as e}from"./query-vendor-DbpRcGHB.js";import{r as x}from"./router-vendor-CX6yTN5-.js";import{c as K,i as X,P,f as $,d as M,b as S,y as J,a1 as Q,r as V,a2 as W,a3 as Y,a4 as N,a5 as h,a6 as Z,a7 as i,B as ee}from"./ui-components-B3WDh88j.js";import{a as te,E as re,n as ne}from"./ui-vendor-BwzdWZX0.js";import{D as se,g as ae,h as oe,i as ce}from"./index-CvPR-Eda.js";import{B}from"./badge-DNy6GwuQ.js";import{u as ie}from"./i18n-vendor-CzM1WOJE.js";import"./react-vendor-DJG_os-6.js";import"./api-client-xsH4HHeE.js";import"./etws-api-BUsVSvPp.js";var E="Checkbox",[de,ye]=K(E),[le,_]=de(E);function he(r){const{__scopeCheckbox:a,checked:o,children:u,defaultChecked:c,disabled:n,form:s,name:p,onCheckedChange:t,required:f,value:j="on",internal_do_not_use_render:m}=r,[b,v]=$({prop:o,defaultProp:c??!1,onChange:t,caller:E}),[C,g]=x.useState(null),[y,d]=x.useState(null),l=x.useRef(!1),w=C?!!s||!!C.closest("form"):!0,R={checked:b,disabled:n,setChecked:v,control:C,setControl:g,name:p,form:s,value:j,hasConsumerStoppedPropagationRef:l,required:f,defaultChecked:k(c)?!1:c,isFormControl:w,bubbleInput:y,setBubbleInput:d};return e.jsx(le,{scope:a,...R,children:ue(m)?m(R):u})}var D="CheckboxTrigger",q=x.forwardRef(({__scopeCheckbox:r,onKeyDown:a,onClick:o,...u},c)=>{const{control:n,value:s,disabled:p,checked:t,required:f,setControl:j,setChecked:m,hasConsumerStoppedPropagationRef:b,isFormControl:v,bubbleInput:C}=_(D,r),g=M(c,j),y=x.useRef(t);return x.useEffect(()=>{const d=n==null?void 0:n.form;if(d){const l=()=>m(y.current);return d.addEventListener("reset",l),()=>d.removeEventListener("reset",l)}},[n,m]),e.jsx(P.button,{type:"button",role:"checkbox","aria-checked":k(t)?"mixed":t,"aria-required":f,"data-state":O(t),"data-disabled":p?"":void 0,disabled:p,value:s,...u,ref:g,onKeyDown:S(a,d=>{d.key==="Enter"&&d.preventDefault()}),onClick:S(o,d=>{m(l=>k(l)?!0:!l),C&&v&&(b.current=d.isPropagationStopped(),b.current||d.stopPropagation())})})});q.displayName=D;var z=x.forwardRef((r,a)=>{const{__scopeCheckbox:o,name:u,checked:c,defaultChecked:n,required:s,disabled:p,value:t,onCheckedChange:f,form:j,...m}=r;return e.jsx(he,{__scopeCheckbox:o,checked:c,defaultChecked:n,disabled:p,required:s,onCheckedChange:f,name:u,form:j,value:t,internal_do_not_use_render:({isFormControl:b})=>e.jsxs(e.Fragment,{children:[e.jsx(q,{...m,ref:a,__scopeCheckbox:o}),b&&e.jsx(L,{__scopeCheckbox:o})]})})});z.displayName=E;var A="CheckboxIndicator",F=x.forwardRef((r,a)=>{const{__scopeCheckbox:o,forceMount:u,...c}=r,n=_(A,o);return e.jsx(X,{present:u||k(n.checked)||n.checked===!0,children:e.jsx(P.span,{"data-state":O(n.checked),"data-disabled":n.disabled?"":void 0,...c,ref:a,style:{pointerEvents:"none",...r.style}})})});F.displayName=A;var H="CheckboxBubbleInput",L=x.forwardRef(({__scopeCheckbox:r,...a},o)=>{const{control:u,hasConsumerStoppedPropagationRef:c,checked:n,defaultChecked:s,required:p,disabled:t,name:f,value:j,form:m,bubbleInput:b,setBubbleInput:v}=_(H,r),C=M(o,v),g=J(n),y=Q(u);x.useEffect(()=>{const l=b;if(!l)return;const w=window.HTMLInputElement.prototype,I=Object.getOwnPropertyDescriptor(w,"checked").set,U=!c.current;if(g!==n&&I){const G=new Event("click",{bubbles:U});l.indeterminate=k(n),I.call(l,k(n)?!1:n),l.dispatchEvent(G)}},[b,g,n,c]);const d=x.useRef(k(n)?!1:n);return e.jsx(P.input,{type:"checkbox","aria-hidden":!0,defaultChecked:s??d.current,required:p,disabled:t,name:f,value:j,form:m,...a,tabIndex:-1,ref:C,style:{...a.style,...y,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});L.displayName=H;function ue(r){return typeof r=="function"}function k(r){return r==="indeterminate"}function O(r){return k(r)?"indeterminate":r?"checked":"unchecked"}function T({className:r,...a}){return e.jsx(z,{"data-slot":"checkbox",className:V("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",r),...a,children:e.jsx(F,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:e.jsx(te,{className:"size-3.5"})})})}const Ne=({users:r,isLoading:a,selectedUsers:o,handleSelectUser:u,handleSelectAll:c,handleEditUser:n})=>{const{t:s}=ie(),p=t=>t==="Blocked"?e.jsx(B,{variant:"destructive",children:s("blocked")}):e.jsx(B,{variant:"default",children:s("active")});return e.jsx("div",{className:"border rounded-lg",children:e.jsxs(W,{children:[e.jsx(Y,{children:e.jsxs(N,{children:[e.jsx(h,{className:"w-12",children:e.jsx(T,{checked:o.length===r.length&&r.length>0,onCheckedChange:c})}),e.jsx(h,{children:s("eln")}),e.jsx(h,{children:s("firstName")}),e.jsx(h,{children:s("surname")}),e.jsx(h,{children:s("familyName")}),e.jsx(h,{children:s("op")}),e.jsx(h,{children:s("region")}),e.jsx(h,{children:s("iptuName")}),e.jsx(h,{children:s("city")}),e.jsx(h,{children:s("clientNumber")}),e.jsx(h,{children:s("status")}),e.jsx(h,{className:"w-16"})]})}),e.jsx(Z,{children:a?e.jsx(N,{children:e.jsxs(i,{colSpan:9,className:"text-center py-8",children:[s("loading"),"..."]})}):r.length===0?e.jsx(N,{children:e.jsx(i,{colSpan:9,className:"text-center py-8",children:s("noUsersFound")})}):r.map(t=>e.jsxs(N,{children:[e.jsx(i,{children:e.jsx(T,{checked:o.includes(t.id||""),onCheckedChange:f=>u(t.id||"",f)})}),e.jsx(i,{className:"font-medium",children:t.eln}),e.jsx(i,{children:t.firstName}),e.jsx(i,{children:t.surname}),e.jsx(i,{children:t.familyName}),e.jsx(i,{children:t.op}),e.jsx(i,{children:t.region}),e.jsx(i,{children:t.iptuName}),e.jsx(i,{children:t.city}),e.jsx(i,{children:t.clientNumber}),e.jsx(i,{children:p(t.blocked)}),e.jsx(i,{children:e.jsxs(se,{children:[e.jsx(ae,{asChild:!0,children:e.jsx(ee,{variant:"ghost",size:"sm",children:e.jsx(re,{className:"h-4 w-4"})})}),e.jsx(oe,{align:"end",children:e.jsxs(ce,{onClick:()=>n(t),children:[e.jsx(ne,{className:"h-4 w-4 mr-2"}),s("edit")]})})]})})]},t.id))})]})})};export{Ne as default};
