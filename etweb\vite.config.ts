import path from "path"
import tailwindcss from "@tailwindcss/vite"
import react from "@vitejs/plugin-react"
import { defineConfig } from "vite"

// https://vite.dev/config/
export default defineConfig({
  plugins: [react(), tailwindcss()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          // Vendor chunks
          'react-vendor': ['react', 'react-dom'],
          'router-vendor': ['react-router', 'react-router'],
          'query-vendor': ['@tanstack/react-query'],
          'ui-vendor': ['lucide-react'],
          'form-vendor': ['react-hook-form', '@hookform/resolvers', 'yup'],
          'i18n-vendor': ['react-i18next', 'i18next'],

          // API chunks
          'api-client': ['axios'],
          'etws-api': ['./src/data/etws'],

          // UI components
          'ui-components': [
            './src/components/ui/button',
            './src/components/ui/input',
            './src/components/ui/label',
            './src/components/ui/card',
            './src/components/ui/dialog',
            './src/components/ui/table',
            './src/components/ui/select',
          ],
        },
      },
    },
  },
  server: {
    proxy: {
      "/et-ws": {
        target: "https://localhost:44356/",
        changeOrigin: false,
        secure: false,
      },
    },
  },
})